import { fetchAuthSession, getCurrentUser } from 'aws-amplify/auth';

export async function getValidToken() {
  try {
    const session = await fetchAuthSession();
    // Amplify automatically uses the Refresh Token if the Access Token is expired
    return session.tokens?.idToken?.toString() || null;
  } catch (error) {
    console.error('Session expired or invalid', error);
    return null;
  }
}

export async function getAmplifyUser() {
  try {
    const user = await getCurrentUser();
    return user;
  } catch (error) {
    return null;
  }
}
