import * as yup from 'yup';

export interface OrganizationFormData {
  name: string;
  contactPersonName: string;
  contactEmail: string;
  contactPhone: string | null;
  address: {
    street1: string;
    city: string;
    state: string;
    postalCode: string;
    country: string;
  };
  description: string | null;
  registrationFee?: number | null;
  isClinicOrganization?: boolean;
}

const organizationAddressSchema = yup.object({
  street1: yup.string().required('Street address is required'),
  city: yup.string().required('City is required'),
  state: yup.string().required('State is required'),
  postalCode: yup
    .string()
    .required('Postal code is required')
    .matches(/^\d{6}$/, 'Postal code must be exactly 6 digits'),
  country: yup.string().required('Country is required'),
});

export const organizationSchema = yup
  .object({
    name: yup.string().required('Organization name is required'),
    registrationFee: yup
      .number()
      .transform((value) => (value === '' || isNaN(value) ? null : value))
      .required('Registration fee is required')
      .typeError('Registration fee must be a number')
      .min(0, 'Registration fee cannot be negative'),

    contactPersonName: yup.string().required('Contact person name is required'),

    contactEmail: yup
      .string()
      .required('Contact email is required')
      .email('Please enter a valid email address')
      .matches(
        /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/,
        'Please enter a valid email address (e.g., <EMAIL>)'
      )
      .transform((value) => (value ? value.trim() : value)),

    contactPhone: yup
      .string()
      .nullable()
      .test(
        'phone',
        'Phone number must have exactly 10 digits after +91',
        (value) => {
          if (!value || value === '+91') return true;

          return /^\+91\d{10}$/.test(value);
        }
      )
      .default(null),

    address: organizationAddressSchema,
    description: yup.string().nullable().default(null),
    isClinicOrganization: yup.boolean().default(false),
  })
  .required();
