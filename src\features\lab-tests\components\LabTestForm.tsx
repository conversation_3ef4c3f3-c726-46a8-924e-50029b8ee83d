import { yupResolver } from '@hookform/resolvers/yup';
import React, { useCallback, useEffect, useState } from 'react';
import { Controller, useForm } from 'react-hook-form';
import * as yup from 'yup';

import AppButton from '../../../components/Common/AppButton';
import TextInput from '../../../components/Common/MUIInput';
import MUISelect from '../../../components/Common/MUISelect';
import {
  fetchLabTestDepartments,
  LabTestDepartment,
} from '../services/department.service';
import { LabTestListItem } from '../types/labTest.types';

// Mapping from API class shortforms to full department names (matching exact API values)
const CLASS_TO_DEPARTMENT_MAPPING: Record<string, string> = {
  SERO: 'Serology',
  MICRO: 'Microbiology',
  RAD: 'Radiology / Imaging Obs',
  'HEM/BC': 'Hematology / Blood Count',
  CHEM: 'Clinical Chemistry',
  PATHOLOGY: 'Surgical/Anatomic Path',
};

// Function to map API class to department name
const mapClassToDepartment = (
  apiClass: string,
  availableDepartments: string[]
): string => {
  // If the API class exists in our mapping, use it
  if (CLASS_TO_DEPARTMENT_MAPPING[apiClass]) {
    const mappedDepartment = CLASS_TO_DEPARTMENT_MAPPING[apiClass];
    // Check if the mapped department exists in available departments
    if (availableDepartments.includes(mappedDepartment)) {
      return mappedDepartment;
    }
  }

  // If not any of the predefined mappings or mapped department not available, default to OTHERS or others
  return availableDepartments.includes('OTHERS')
    ? 'OTHERS'
    : availableDepartments.includes('others')
      ? 'others'
      : availableDepartments[0] || 'OTHERS';
};

// Create dynamic form schema
const createLabTestFormSchema = (departmentValues: string[]) =>
  yup.object({
    displayName: yup
      .string()
      .max(100, 'Display name must not exceed 100 characters'),
    testName: yup
      .string()
      .trim()
      .required('Test name is required')
      .min(2, 'Test name must be at least 2 characters'),
    department: yup
      .string()
      .oneOf(departmentValues, 'Please select a valid department')
      .required('Department is required'),
    organizationCost: yup
      .number()
      .typeError('Please enter a valid number')
      .min(0, 'Price cannot be negative')
      .required('Price is required')
      .transform((value) => (isNaN(value) || value === '' ? 0 : value)),
    isActive: yup.boolean().required('Please select status'),
  });

interface LabTestFormData {
  displayName?: string | undefined;
  testName: string;
  department: string;
  organizationCost: number;
  isActive: boolean;
}

interface LabTestFormProps {
  test: LabTestListItem | null;
  onSubmit: (
    testId: string,
    data: {
      displayName?: string | null;
      testName?: string;
      department: string;
      organizationCost: number;
      isActive: boolean;
    }
  ) => Promise<void>;
  onSuccess?: () => void;
  onClose: () => void;
  updating?: boolean;
}

const LabTestForm: React.FC<LabTestFormProps> = ({
  test,
  onSubmit,
  onSuccess,
  onClose,
  updating = false,
}) => {
  const [departments, setDepartments] = useState<LabTestDepartment[]>([]);
  const [loadingDepartments, setLoadingDepartments] = useState(true);

  // Create form schema with current department values
  const labTestFormSchema = createLabTestFormSchema(
    departments.filter((d) => d.value !== 'ALL').map((d) => d.value)
  );

  const {
    control,
    handleSubmit,
    formState: { errors, isSubmitting },
    reset,
  } = useForm<LabTestFormData>({
    resolver: yupResolver(labTestFormSchema) as any,
    defaultValues: {
      displayName: test?.displayName ?? '',
      testName: test?.testName ?? '',
      department: 'ALL',
      organizationCost: test?.organizationCost ?? 0,
      isActive: true,
    },
  });

  // Load departments on component mount
  useEffect(() => {
    const loadDepartments = async () => {
      try {
        setLoadingDepartments(true);
        const fetchedDepartments = await fetchLabTestDepartments();
        setDepartments(fetchedDepartments);
      } catch (error) {
        console.error('Failed to load departments:', error);
        // Set fallback departments
        setDepartments([
          { value: 'CHEM', label: 'Clinical Chemistry' },
          { value: 'HEM/BC', label: 'Hematology/Blood Count' },
          { value: 'MICRO', label: 'Microbiology' },
          { value: 'SERO', label: 'Serology' },
          { value: 'RAD', label: 'Radiology/Imaging Obs' },
          { value: 'OTHERS', label: 'Others' },
        ]);
      } finally {
        setLoadingDepartments(false);
      }
    };

    loadDepartments();
  }, []);

  // Reset form when test changes
  useEffect(() => {
    if (test && departments.length > 0) {
      let departmentToUse = 'OTHERS';

      // Approach 1: Use test.class field and map it
      if (test.class) {
        const departmentValues = departments
          .filter((d) => d.value !== 'ALL')
          .map((d) => d.value);
        const mappedDepartment = mapClassToDepartment(
          test.class,
          departmentValues
        );
        departmentToUse = mappedDepartment;
      }
      // Approach 2: Use first department from departments array
      else if (test.departments && test.departments.length > 0) {
        departmentToUse = test.departments[0] || 'OTHERS';
      }

      // Ensure the department is valid against loaded departments
      const departmentValues = departments
        .filter((d) => d.value !== 'ALL')
        .map((d) => d.value);

      const validDepartment = departmentValues.includes(departmentToUse)
        ? departmentToUse
        : 'ALL';

      reset({
        displayName: test.displayName ?? '',
        testName: test.testName ?? '',
        department: validDepartment,
        organizationCost: test.organizationCost ?? 0,
        isActive: test.isActive,
      });
    } else if (!test && departments.length > 0) {
      reset({
        displayName: '',
        testName: '',
        department: 'ALL',
        organizationCost: 0,
        isActive: true,
      });
    }
  }, [test, departments, reset]);

  const onFormSubmit = useCallback(
    async (data: LabTestFormData) => {
      if (!test) return;

      try {
        await onSubmit(test.testId, {
          displayName: data.displayName || null,
          testName: data.testName,
          department: data.department || 'OTHERS',
          organizationCost: data.organizationCost,
          isActive: data.isActive,
        });

        onSuccess?.();
        onClose();
      } catch (error) {
        console.error('Error saving test:', error);
        // Error is already handled by the parent component
      }
    },
    [test, onSubmit, onSuccess, onClose]
  );

  if (!test) return null;

  const isCustomTest = test?.isCustomTest ?? false;

  return (
    <form onSubmit={handleSubmit(onFormSubmit)} className='space-y-6 p-6'>
      <Controller
        name='testName'
        control={control}
        render={({ field }) => (
          <TextInput
            {...field}
            label='Test Name *'
            error={!!errors.testName}
            helperText={errors.testName?.message}
            placeholder='Enter test name'
            disabled={!isCustomTest}
          />
        )}
      />
      <Controller
        name='displayName'
        control={control}
        render={({ field }) => (
          <TextInput
            {...field}
            label='Display Name'
            error={!!errors.displayName}
            helperText={errors.displayName?.message}
            placeholder='Enter display name (optional)'
            disabled={isSubmitting || updating}
          />
        )}
      />

      <Controller
        name='department'
        control={control}
        render={({ field }) => (
          <MUISelect
            {...field}
            label='Department *'
            options={departments
              .filter(
                (dept) =>
                  dept.value !== 'ALL' &&
                  dept.label !== 'All'
              )
              .map((dept) => ({
                label: dept.label,
                value: dept.value,
              }))}
            error={!!errors.department}
            helperText={errors.department?.message}
            disabled={loadingDepartments}
          />
        )}
      />

      <Controller
        name='organizationCost'
        control={control}
        render={({ field }) => (
          <TextInput
            {...field}
            label='Organization Cost *'
            value={field.value?.toString() ?? ''}
            onChange={(e) => {
              const value = e.target.value;
              if (value === '') {
                field.onChange(0);
              } else {
                const numericValue = Number(value);
                if (numericValue < 0) {
                  return;
                }
                field.onChange(numericValue);
              }
            }}
            error={!!errors.organizationCost}
            helperText={errors.organizationCost?.message}
            placeholder='Enter amount'
            type='number'
            inputProps={{
              min: 0,
              step: 0.01,
              style: {
                MozAppearance: 'textfield', // Firefox
              },
            }}
            sx={{
              '& input[type=number]': {
                MozAppearance: 'textfield', // Firefox
              },
              '& input[type=number]::-webkit-outer-spin-button': {
                WebkitAppearance: 'none', // Chrome, Safari, Edge
                margin: 0,
              },
              '& input[type=number]::-webkit-inner-spin-button': {
                WebkitAppearance: 'none', // Chrome, Safari, Edge
                margin: 0,
              },
            }}
          />
        )}
      />

      {/* Status Select */}
      <Controller
        name='isActive'
        control={control}
        render={({ field }) => (
          <MUISelect
            {...field}
            label='Status *'
            options={[
              { label: 'Active', value: 'true' },
              { label: 'Inactive', value: 'false' },
            ]}
            error={!!errors.isActive}
            helperText={errors.isActive?.message}
            onChange={(e) => field.onChange(e.target.value === 'true')}
            value={field.value ? 'true' : 'false'}
          />
        )}
      />

      {/* Form Actions */}
      <div className='flex justify-end space-x-4 pt-4'>
        <AppButton
          type='button'
          kind='secondary'
          onClick={onClose}
          disabled={isSubmitting || updating}
        >
          Cancel
        </AppButton>
        <AppButton
          type='submit'
          kind='primary'
          disabled={isSubmitting || updating}
        >
          {isSubmitting || updating ? 'Saving...' : 'Save'}
        </AppButton>
      </div>
    </form>
  );
};

export default LabTestForm;
