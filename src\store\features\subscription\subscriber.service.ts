import { SUBSCRIBERS_ENDPOINT } from '../../../constants/api-endpoints';
import api from '../../../services/api';

export type SubscriberStatus =
  | 'active'
  | 'expired'
  | 'cancelled'
  | 'pending'
  | 'free trial';

export type SubscriberBillingType = 'Monthly' | 'Yearly' | '7 Days';

export interface CreateSubscriberRequest {
  organizationName: string;
  email: string;
  contactPerson: string;
  contactPhone?: string;
  address: string;
  pincode: string;
  pan: string;
  gstin: string;
  planId: string;
  billingType: SubscriberBillingType;
  status: SubscriberStatus;
  endDate: string | undefined;
  validity?: string | undefined;
  autoRenew: boolean;
  description?: string;
  totalAmount?: number | null;
}

const normalizeRequestBillingType = (
  billingType: SubscriberBillingType
): 'monthly' | 'yearly' => {
  const normalized = billingType.toLowerCase();
  // Normalize '7 days' to 'monthly' for backend compatibility
  if (normalized === '7 days') {
    return 'monthly';
  }
  return normalized === 'yearly' ? 'yearly' : 'monthly';
};

export interface CreateSubscriberResponse {
  success?: boolean;
  message?: string;
  data?: unknown;
}

export interface SubscriberDetail extends CreateSubscriberRequest {
  id?: string;
  subscriptionType?: string;
  paymentAmount?: number | null;
  startDate?: string | undefined;
}

const pickString = (...values: unknown[]): string => {
  for (const value of values) {
    if (typeof value === 'string' && value.trim().length > 0) {
      return value.trim();
    }
  }
  return '';
};

const parseBoolean = (value: unknown): boolean => {
  if (typeof value === 'boolean') return value;
  if (typeof value === 'number') return value === 1;
  if (typeof value === 'string') {
    const normalized = value.trim().toLowerCase();
    if (['true', '1', 'yes', 'y'].includes(normalized)) return true;
    if (['false', '0', 'no', 'n'].includes(normalized)) return false;
  }
  return false;
};

const parseDateString = (value: unknown): string | undefined => {
  if (!value) return undefined;
  if (value instanceof Date && !Number.isNaN(value.getTime())) {
    return value.toISOString().split('T')[0];
  }
  if (typeof value === 'number' && Number.isFinite(value)) {
    const date = new Date(value);
    if (!Number.isNaN(date.getTime())) {
      return date.toISOString().split('T')[0];
    }
  }
  if (typeof value === 'string') {
    const trimmed = value.trim();
    if (!trimmed) return undefined;
    const parsed = Date.parse(trimmed);
    if (!Number.isNaN(parsed)) {
      return new Date(parsed).toISOString().split('T')[0];
    }
    return trimmed;
  }
  return undefined;
};

const parseStatus = (value: unknown): SubscriberStatus => {
  const normalized = pickString(value).toLowerCase();
  switch (normalized) {
    case 'expired':
      return 'expired';
    case 'cancelled':
    case 'canceled':
      return 'cancelled';
    case 'pending':
      return 'pending';
    case 'inactive':
      return 'pending';
    case 'free trial':
    case 'freetrial':
    case 'free_trial':
      return 'free trial';
    case 'active':
    default:
      return 'active';
  }
};

const parseBillingType = (value: unknown): SubscriberBillingType => {
  const normalized = pickString(value).toLowerCase();
  if (normalized === 'yearly') {
    return 'Yearly';
  }
  // Check for 7 days variations
  if (
    normalized === '7 days' ||
    normalized === '7days' ||
    normalized === 'sevendays'
  ) {
    return '7 Days';
  }
  return 'Monthly';
};

const isRecord = (value: unknown): value is Record<string, unknown> =>
  typeof value === 'object' && value !== null && !Array.isArray(value);

const parseNumberValue = (value: unknown): number | undefined => {
  if (typeof value === 'number' && Number.isFinite(value)) {
    return value;
  }
  if (typeof value === 'string') {
    const trimmed = value.trim();
    if (!trimmed) {
      return undefined;
    }
    const parsed = Number(trimmed);
    if (Number.isFinite(parsed)) {
      return parsed;
    }
  }
  return undefined;
};

const getNestedValue = (
  source: unknown,
  path: string[]
): unknown | undefined => {
  if (!path.length) {
    return source;
  }

  let current: unknown = source;

  for (const key of path) {
    if (!isRecord(current)) {
      return undefined;
    }
    current = current[key];
  }

  return current;
};

const extractNumberFromSources = (
  sources: unknown[],
  paths: string[][]
): number | undefined => {
  for (const source of sources) {
    if (source === undefined || source === null) {
      continue;
    }

    for (const path of paths) {
      const value = getNestedValue(source, path);
      const parsed = parseNumberValue(value);
      if (parsed !== undefined) {
        return parsed;
      }
    }
  }

  return undefined;
};

export interface GetSubscribersParams {
  search?: string;
  planId?: string;
  status?: SubscriberStatus | string;
  page?: number;
  limit?: number;
  setupStatus?: string;
  requestedOrganizationName?: string;
}

export interface GetSubscribersResponse {
  items: unknown[];
  raw: unknown;
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

export const getSubscribers = async (
  params: GetSubscribersParams = {}
): Promise<GetSubscribersResponse> => {
  const defaultPage = Math.max(1, Math.floor(params.page ?? 1));
  const defaultLimit = Math.max(1, Math.floor(params.limit ?? 100));
  const queryParams: Record<string, string> = {
    page: String(defaultPage),
    limit: String(defaultLimit),
  };

  if (params.search && params.search.trim().length > 0) {
    queryParams.search = params.search.trim();
  }

  if (params.planId) {
    queryParams.planId = params.planId;
  }

  if (params.status) {
    queryParams.status = String(params.status).trim();
  }

  if (params.setupStatus) {
    queryParams.setupStatus = params.setupStatus.trim();
  }

  if (params.requestedOrganizationName) {
    queryParams.requestedOrganizationName =
      params.requestedOrganizationName.trim();
  }

  const response = await api.get(SUBSCRIBERS_ENDPOINT, {
    params: queryParams,
  });

  const responseData = response.data as unknown;

  const candidateSources: unknown[] = [responseData];
  if (isRecord(responseData)) {
    const nestedCandidates = [
      responseData['data'],
      responseData['result'],
      responseData['results'],
      responseData['payload'],
      responseData['meta'],
      responseData['metadata'],
      responseData['pagination'],
    ];
    candidateSources.push(...nestedCandidates.filter(Boolean));
  }

  const totalPaths: string[][] = [
    ['total'],
    ['totalCount'],
    ['totalRecords'],
    ['totalItems'],
    ['totalItemCount'],
    ['count'],
    ['recordsTotal'],
    ['pagination', 'total'],
    ['pagination', 'totalCount'],
    ['pagination', 'totalRecords'],
    ['pagination', 'totalItems'],
    ['meta', 'total'],
    ['meta', 'totalCount'],
    ['meta', 'totalRecords'],
    ['metadata', 'total'],
    ['metadata', 'totalCount'],
    ['metadata', 'totalRecords'],
    ['data', 'total'],
    ['data', 'totalCount'],
    ['data', 'totalRecords'],
    ['payload', 'total'],
    ['payload', 'totalCount'],
  ];

  const pagePaths: string[][] = [
    ['page'],
    ['currentPage'],
    ['pageNumber'],
    ['page_index'],
    ['pagination', 'page'],
    ['pagination', 'currentPage'],
    ['pagination', 'pageNumber'],
    ['pagination', 'page_index'],
    ['meta', 'page'],
    ['meta', 'currentPage'],
    ['metadata', 'page'],
    ['metadata', 'currentPage'],
    ['data', 'page'],
    ['data', 'currentPage'],
    ['payload', 'page'],
  ];

  const limitPaths: string[][] = [
    ['limit'],
    ['pageSize'],
    ['page_size'],
    ['size'],
    ['perPage'],
    ['per_page'],
    ['pagination', 'limit'],
    ['pagination', 'pageSize'],
    ['pagination', 'size'],
    ['pagination', 'perPage'],
    ['pagination', 'page_size'],
    ['meta', 'pageSize'],
    ['meta', 'limit'],
    ['metadata', 'pageSize'],
    ['metadata', 'limit'],
    ['data', 'pageSize'],
    ['data', 'limit'],
    ['payload', 'pageSize'],
  ];

  const totalPagesPaths: string[][] = [
    ['totalPages'],
    ['pages'],
    ['pageCount'],
    ['pagination', 'totalPages'],
    ['pagination', 'pages'],
    ['pagination', 'pageCount'],
    ['meta', 'totalPages'],
    ['meta', 'pages'],
    ['metadata', 'totalPages'],
    ['metadata', 'pages'],
    ['data', 'totalPages'],
    ['data', 'pages'],
    ['payload', 'totalPages'],
  ];

  const extractedTotal =
    extractNumberFromSources(candidateSources, totalPaths) ?? undefined;
  const extractedPage =
    extractNumberFromSources(candidateSources, pagePaths) ?? undefined;
  const extractedLimit =
    extractNumberFromSources(candidateSources, limitPaths) ?? undefined;
  const extractedTotalPages =
    extractNumberFromSources(candidateSources, totalPagesPaths) ?? undefined;

  const extractItems = (data: unknown): unknown[] | null => {
    if (Array.isArray(data)) {
      return data;
    }

    if (data && typeof data === 'object') {
      const record = data as Record<string, unknown>;
      const candidates = [
        record['subscribers'],
        record['data'],
        record['items'],
        record['results'],
      ];

      for (const candidate of candidates) {
        if (Array.isArray(candidate)) {
          return candidate;
        }
      }
    }

    return null;
  };

  const items = extractItems(responseData);

  if (!items) {
    throw new Error('Unexpected subscribers response shape');
  }

  const total = Math.max(
    0,
    Math.floor(extractedTotal ?? (Array.isArray(items) ? items.length : 0))
  );
  const limit = Math.max(1, Math.floor(extractedLimit ?? defaultLimit));
  const page = Math.max(1, Math.floor(extractedPage ?? defaultPage));
  const totalPages = Math.max(
    0,
    Math.floor(
      extractedTotalPages ??
        (limit > 0 ? Math.ceil(total / limit) : items.length > 0 ? 1 : 0)
    )
  );

  return {
    items,
    raw: responseData,
    total,
    page,
    limit,
    totalPages,
  };
};

export const createSubscriber = async (
  payload: CreateSubscriberRequest
): Promise<CreateSubscriberResponse> => {
  const requestPayload = {
    ...payload,
    billingType: normalizeRequestBillingType(payload.billingType),
  };
  const response = await api.post<CreateSubscriberResponse>(
    SUBSCRIBERS_ENDPOINT,
    requestPayload
  );
  return response.data;
};

export const updateSubscriber = async (
  subscriberId: string,
  payload: CreateSubscriberRequest
): Promise<CreateSubscriberResponse> => {
  const requestPayload = {
    ...payload,
    billingType: normalizeRequestBillingType(payload.billingType),
  };
  const response = await api.patch<CreateSubscriberResponse>(
    `${SUBSCRIBERS_ENDPOINT}?id=${subscriberId}`,
    requestPayload
  );
  return response.data;
};

export const getSubscriberById = async (
  subscriberId: string
): Promise<SubscriberDetail> => {
  const response = await api.get(SUBSCRIBERS_ENDPOINT, {
    params: { id: subscriberId },
  });

  const responseData = (response.data ?? {}) as Record<string, unknown>;
  const raw = (responseData.subscriber ??
    responseData.data ??
    responseData.item ??
    responseData) as Record<string, unknown>;

  const data = raw;
  const organization = (data.organization ?? data.organisation) as
    | Record<string, unknown>
    | undefined;
  const admin = data.admin as Record<string, unknown> | undefined;
  const user = data.user as Record<string, unknown> | undefined;
  const plan = data.plan as Record<string, unknown> | undefined;
  const subscription = data.subscription as Record<string, unknown> | undefined;
  const subscriptionHistoryRaw = Array.isArray(data.subscriptionHistory)
    ? (data.subscriptionHistory as Record<string, unknown>[])
    : [];
  const prioritizedHistoryEntry =
    subscriptionHistoryRaw.find((entry) => {
      const status = pickString(entry.status).toLowerCase();
      return ['active', 'pending'].includes(status);
    }) ?? subscriptionHistoryRaw[0];
  const selectedHistoryEntry = prioritizedHistoryEntry as
    | Record<string, unknown>
    | undefined;
  const activeSubscription = data.activeSubscription as
    | Record<string, unknown>
    | undefined;
  const address = data.address as Record<string, unknown> | undefined;

  const totalAmountNumber = extractNumberFromSources(
    [selectedHistoryEntry, activeSubscription, subscription, plan, data],
    [
      ['totalAmount'],
      ['amount'],
      ['price'],
      ['subscriptionAmount'],
      ['planAmount'],
      ['billingAmount'],
      ['total'],
      ['monthlyTotal'],
      ['yearlyTotal'],
      ['totalMonthlyBasicAmount'],
      ['totalYearlyBasicAmount'],
    ]
  );

  const formattedAddress = (() => {
    if (!address) return undefined;
    const parts = [
      pickString(address.street, address.addressLine1, address.line1),
      pickString(address.city, address.town),
      pickString(address.state),
      pickString(address.country),
    ]
      .filter((part) => part && part.length > 0)
      .join(', ');
    return parts || undefined;
  })();

  const autoRenewSource =
    data.autoRenew ??
    data.autoRenewal ??
    data.isAutoRenew ??
    data.autoRenewed ??
    activeSubscription?.autoRenew;

  const statusRaw = pickString(
    data.status,
    data.subscriptionStatus,
    data.state,
    activeSubscription?.status
  );
  const fallbackStatus = parseBoolean(data.isActive) ? 'active' : 'pending';

  const startDateSource =
    selectedHistoryEntry?.startDate ??
    selectedHistoryEntry?.start_date ??
    activeSubscription?.startDate ??
    activeSubscription?.start_date ??
    subscription?.startDate ??
    subscription?.start_date ??
    data.startDate ??
    data.start_date;
  const startDate = parseDateString(startDateSource);

  const endDateSource =
    selectedHistoryEntry?.endDate ??
    selectedHistoryEntry?.end_date ??
    activeSubscription?.endDate ??
    activeSubscription?.expiryDate ??
    subscription?.endDate ??
    subscription?.end_date ??
    data.endDate ??
    data.end_date ??
    data.subscriptionExpiry ??
    data.expiryDate ??
    data.expiry ??
    data.validity ??
    data.validityDate ??
    selectedHistoryEntry?.validity ??
    activeSubscription?.validity ??
    subscription?.validity;
  const endDate = parseDateString(endDateSource);

  return {
    id: pickString(
      data.id,
      data.subscriberId,
      data._id,
      user?.id,
      organization?.id
    ),
    organizationName: pickString(
      data.organizationName,
      data.organisationName,
      organization?.name,
      data.name
    ),
    email: pickString(
      data.email,
      data.adminEmail,
      data.contactEmail,
      data.contactEmail,
      admin?.email,
      user?.email
    ),
    contactPerson: pickString(
      data.contactPerson,
      data.adminName,
      data.contactName,
      data.contactPersonName,
      admin?.name
    ),
    contactPhone: pickString(
      data.contactPhone,
      data.phone,
      data.contactNumber,
      admin?.phone,
      activeSubscription?.contactPhone
    ),
    address: pickString(
      data.address,
      data.organizationAddress,
      data.organisationAddress,
      organization?.address,
      formattedAddress
    ),
    pincode: pickString(
      data.pincode,
      data.pin,
      data.zipCode,
      data.postalCode,
      address?.pincode
    ),
    pan: pickString(data.pan, data.PAN),
    gstin: pickString(data.gstin, data.GSTIN),
    planId: pickString(
      selectedHistoryEntry?.planId,
      activeSubscription?.planId,
      subscription?.planId,
      data.planId,
      data.subscriptionPlanId,
      plan?.id
    ),
    billingType: parseBillingType(
      pickString(
        selectedHistoryEntry?.billingType,
        activeSubscription?.billingType,
        subscription?.billingType,
        data.billingType,
        plan?.billingType
      ) ||
        pickString(
          selectedHistoryEntry?.subscriptionType,
          activeSubscription?.subscriptionType,
          subscription?.subscriptionType,
          data.subscriptionType,
          plan?.subscriptionType,
          data.planType,
          plan?.type
        ) ||
        pickString(
          selectedHistoryEntry?.validity,
          activeSubscription?.validity,
          subscription?.validity,
          data.validity,
          plan?.validity
        )
    ),
    status: parseStatus(statusRaw || fallbackStatus),
    ...(startDate ? { startDate } : {}),
    endDate,
    validity: endDate,
    autoRenew: parseBoolean(autoRenewSource),
    description: pickString(
      data.description,
      data.notes,
      data.remark,
      activeSubscription?.description
    ),
    totalAmount: totalAmountNumber ?? null,
    subscriptionType: pickString(
      selectedHistoryEntry?.subscriptionType,
      activeSubscription?.subscriptionType,
      subscription?.subscriptionType,
      data.subscriptionType,
      plan?.subscriptionType
    ),
    paymentAmount:
      extractNumberFromSources(
        [selectedHistoryEntry, activeSubscription, subscription, data],
        [['paymentAmount'], ['payment_amount']]
      ) ?? null,
  };
};

// New API functions for Clinical Way Requested Subscribers
export interface MapUserToClinicOrgRequest {
  organizationId: string;
  subscriptionIds: string[];
}

export interface UnmapUserFromClinicOrgRequest {
  organizationId: string;
  subscriptionIds: string[];
}

// API Response interfaces
export interface UserResult {
  id: string;
  email: string;
  organizationId: string;
}

export interface SuccessResult {
  subscriptionId: string;
  message: string;
  user: UserResult;
}

export interface FailedResult {
  subscriptionId?: string;
  message?: string;
  error?: string;
}

export interface MapUnmapApiResponse {
  success: boolean;
  totalProcessed: number;
  successCount: number;
  failedCount: number;
  results: {
    success: SuccessResult[];
    failed: FailedResult[];
  };
  message?: string;
}

export const mapUserToClinicOrg = async (
  payload: MapUserToClinicOrgRequest
): Promise<MapUnmapApiResponse> => {
  const response = await api.post('/map-user-to-clinic-org', payload);
  return response.data;
};

export const unmapUserFromClinicOrg = async (
  payload: UnmapUserFromClinicOrgRequest
): Promise<MapUnmapApiResponse> => {
  const response = await api.post('/unmap-user-from-clinic-org', payload);
  return response.data;
};
