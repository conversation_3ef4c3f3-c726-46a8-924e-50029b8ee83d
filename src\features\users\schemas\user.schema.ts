import * as yup from 'yup';

export const userFormSchema = yup.object().shape({
  name: yup.string().required('Name is required'),
  email: yup
    .string()
    .required('Email is required')
    .matches(
      /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/,
      'Please enter a valid email address '
    )
    .test(
      'has-valid-domain',
      'Email must include a valid domain (e.g., .com, .org, etc.)',
      (value) => {
        if (!value) return false;
        const [, domain] = value.split('@');
        return domain ? domain.includes('.') : false;
      }
    )
    .max(100, 'Email must not exceed 100 characters'),
  userRole: yup
    .string()
    .required('User role is required')
    .min(1, 'Please select a role'),
  roleId: yup.string().required('Role ID is required'),
  status: yup
    .string()
    .oneOf(['active', 'inactive'], 'Invalid status')
    .required('Status is required'),
  consultationFee: yup
    .number()
    .nullable()
    .min(0, 'Consultation fee cannot be negative')
    .typeError('Please enter a valid number'),
});

type UserFormSchemaBase = yup.InferType<typeof userFormSchema>;

export interface UserFormSchema
  extends Omit<UserFormSchemaBase, 'consultationFee'> {
  consultationFee?: number | null;
}
