import { ORGANIZATIONS_ENDPOINT } from '../../../constants/api-endpoints';
import api from '../../../services/api';

const apiUrl = ORGANIZATIONS_ENDPOINT;
const pagination = { size: 10 };

export type OrganizationParams = {
  name?: string; // Changed from search to name to match API
  status?: string;
  page?: number;
  pageSize?: number; // Changed from size to pageSize to match API
  isClinicOrganization?: boolean;
};

export type Organization = {
  name: string;
  contactPersonName: string;
  contactEmail: string;
  contactPhone?: string;
  address: {
    street: string;
    city: string;
    state: string;
    postalCode: string;
    country: string;
  };
  description?: string;
  registrationFee?: number | null;
  subscriberId?: string;
};

const fetchOrganizations = async ({
  name = '',
  status = '',
  page = 1,
  pageSize = pagination.size,
}: OrganizationParams = {}) => {
  // Build query parameters for the API
  const queryParams: Record<string, string | number> = {
    pageSize,
  };

  const hasSearchTerm = name && name.trim();
  if (!hasSearchTerm) {
    queryParams.page = page;
  }

  // Add name filter if provided
  if (hasSearchTerm) {
    queryParams.name = name.trim();
  }

  // Add status filter if provided
  if (status) {
    queryParams.status = status.trim();
  }

  const response = await api.get(`${apiUrl}/list-organizations`, {
    params: queryParams,
  });

  // Handle actual API response structure
  // API returns { organizations: Organization[], total: number, page: number, pageSize: number, nextToken: string }
  return {
    data: response.data.organizations || [],
    total: response.data.total || 0,
    page: response.data.page || page,
    pageSize: response.data.pageSize || pageSize,
    totalPages: Math.ceil((response.data.total || 0) / pageSize),
  };
};

const fetchOrganizationById = async (id: string) => {
  const response = await api.get(`${apiUrl}/organization?organizationId=${id}`);
  // Return the organization data directly from response.data
  // The API response structure has the organization object directly in data
  const orgData = response.data;

  // Transform the data to match our Organization type
  return {
    ...orgData,
    status: orgData.isActive ? 'active' : 'inactive', // Add status field based on isActive
  };
};

const createOrganization = async (data: Organization) => {
  const response = await api.post(`${apiUrl}/organization`, data);
  return response.data.data;
};

const updateOrganization = async (data: Organization) => {
  const response = await api.patch(`${apiUrl}/organization`, data);
  return response.data.data;
};

const deleteOrganization = async (id: string) => {
  const response = await api.delete(`${apiUrl}/organization`, {
    params: { organizationId: id },
  });
  return response.data;
};

const toggleOrganizationStatus = async (id: string) => {
  const response = await api.patch(`${apiUrl}/toggle-status`, { id });
  return response.data.data;
};

const fetchAllOrganizations = async (params?: {
  isClinicOrganization?: boolean;
}) => {
  // Fetch all active organizations for dropdown (no pagination)
  const queryParams: any = {
    pageSize: 1000, // Large number to get all organizations
    page: 1,
  };

  // Add isClinicOrganization filter if provided
  if (params?.isClinicOrganization !== undefined) {
    queryParams.isClinicOrganization = params.isClinicOrganization;
  }

  const response = await api.get(`${apiUrl}/list-organizations`, {
    params: queryParams,
  });

  return {
    data: response.data.organizations || [],
    total: response.data.total || 0,
  };
};

const organizationService = {
  fetchOrganizations,
  fetchAllOrganizations,
  fetchOrganizationById,
  createOrganization,
  updateOrganization,
  deleteOrganization,
  toggleOrganizationStatus,
};

export default organizationService;
