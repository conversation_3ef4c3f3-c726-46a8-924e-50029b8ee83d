import { X } from 'lucide-react';
import React, { useEffect, useState } from 'react';

import AppButton from '../../../components/Common/AppButton';
import Modal from '../../../components/Common/Modal';

interface RemoveFromOrganizationModalProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: (
    organizationId: string,
    subscriptionIds: string[]
  ) => Promise<void>;
  selectedSubscribers: Array<{
    id: string;
    name: string;
    email: string;
    currentOrganization?: string;
  }>;
  organizationId: string;
  loading?: boolean;
}

const RemoveFromOrganizationModal: React.FC<
  RemoveFromOrganizationModalProps
> = ({
  isOpen,
  onClose,
  onConfirm,
  selectedSubscribers,
  organizationId,
  loading = false,
}) => {
  const [internalSelectedSubscribers, setInternalSelectedSubscribers] =
    useState(selectedSubscribers);

  useEffect(() => {
    setInternalSelectedSubscribers(selectedSubscribers);
  }, [selectedSubscribers]);

  const handleRemoveSubscriber = (subscriberId: string) => {
    setInternalSelectedSubscribers((prev) =>
      prev.filter((sub) => sub.id !== subscriberId)
    );
  };

  const handleConfirm = async () => {
    if (internalSelectedSubscribers.length === 0) return;

    const subscriptionIds = internalSelectedSubscribers.map((sub) => sub.id);
    await onConfirm(organizationId, subscriptionIds);

    // Reset form after successful operation
    setInternalSelectedSubscribers([]);
  };

  const handleClose = () => {
    if (!loading) {
      setInternalSelectedSubscribers([]);
      onClose();
    }
  };

  const isConfirmDisabled = internalSelectedSubscribers.length === 0 || loading;

  return (
    <Modal
      isOpen={isOpen}
      onClose={handleClose}
      title='Remove from Organization'
      size='md'
    >
      <div className='space-y-4'>
        {/* Confirmation Message */}
        <div className='bg-yellow-50 border border-yellow-200 rounded-lg p-4'>
          <p className='text-sm text-yellow-800'>
            Are you sure you want to remove {internalSelectedSubscribers.length}{' '}
            subscriber{internalSelectedSubscribers.length !== 1 ? 's' : ''} from
            their current organizations?
          </p>
        </div>

        {/* Selected Subscribers List */}
        <div>
          <label className='block text-sm font-medium text-gray-700 mb-2'>
            Selected Subscribers ({internalSelectedSubscribers.length})
          </label>
          <div className='max-h-60 overflow-y-auto space-y-2 border border-gray-200 rounded-lg p-3'>
            {internalSelectedSubscribers.length === 0 ? (
              <p className='text-gray-500 text-sm'>No subscribers selected</p>
            ) : (
              internalSelectedSubscribers.map((subscriber) => (
                <div
                  key={subscriber.id}
                  className='flex items-center justify-between bg-gray-50 p-3 rounded-lg'
                >
                  <div className='flex-1 min-w-0'>
                    <p className='text-sm font-medium text-gray-900 truncate'>
                      {subscriber.name}
                    </p>
                    <p className='text-sm text-gray-500 truncate'>
                      {subscriber.email}
                    </p>
                    {subscriber.currentOrganization && (
                      <p className='text-xs text-blue-600 truncate'>
                        Current: {subscriber.currentOrganization}
                      </p>
                    )}
                  </div>
                  <button
                    onClick={() => handleRemoveSubscriber(subscriber.id)}
                    className='ml-2 p-1 text-gray-400 hover:text-red-600 transition-colors'
                    title='Remove subscriber'
                    disabled={loading}
                  >
                    <X className='w-4 h-4' />
                  </button>
                </div>
              ))
            )}
          </div>
        </div>

        {/* Action Buttons */}
        <div className='flex justify-end space-x-3 pt-4 border-t'>
          <AppButton onClick={handleClose} disabled={loading} kind='secondary'>
            Cancel
          </AppButton>
          <AppButton
            onClick={handleConfirm}
            disabled={isConfirmDisabled}
            kind='primary'
            startIcon={
              loading ? (
                <div className='w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin' />
              ) : null
            }
            style={{ backgroundColor: '#dc2626' }}
          >
            {loading ? 'Removing...' : 'Remove from Organization'}
          </AppButton>
        </div>
      </div>
    </Modal>
  );
};

export default RemoveFromOrganizationModal;
