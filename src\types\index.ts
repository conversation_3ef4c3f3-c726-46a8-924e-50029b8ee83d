export interface User {
  id: string;
  firstName: string;
  lastName: string;
  name: string;
  email: string;
  phone?: string;
  roles: Role[];
  userRole: string;
  status: 'active' | 'inactive';
  organizationId: string;
  organization?: Organization;
  lastLogin?: Date;
  createdAt: Date;
  mustResetPassword: boolean;
  organizationName?: string;
  roleId?: string;
  isOrganizationMainAdmin?: boolean;
  consultationFee?: number | null;
  subscriptionExpiryDate?: Date | null;
  subscriberId?: string;
}

export interface Organization {
  id?: string | null;
  name: string;
  address: {
    street: string;
    city: string;
    state: string;
    postalCode: string;
    country: string;
  };
  contactPersonName: string;
  contactEmail: string;
  contactPhone?: string;
  description?: string;
  registrationFee?: number | null;
  isActive?: boolean;
  status?: 'active' | 'inactive';
  subscriberId?: string;
  isClinicOrganization?: boolean;
}

export interface Role {
  id: string;
  name: string;
  description?: string;
  permissions: Permission[];
  organizationId: string;
  departmentId?: string;
  isSystem: boolean;
  isDefault?: boolean;
  createdAt: Date;
}

export interface Permission {
  id: string;
  key?: string;
  api: string;
  methods: string[];

  module?: 'EMR' | 'MRD' | 'ADMIN' | 'Billing';
  feature?: string;
  actions?: ('create' | 'read' | 'update' | 'delete' | 'print' | 'export')[];
}

export type ModuleType = 'EMR' | 'MRD' | 'ADMIN' | 'Billing';
export type PermissionAction =
  | 'create'
  | 'read'
  | 'update'
  | 'delete'
  | 'print'
  | 'export';

export interface Department {
  id: string;
  name: string;
  description?: string;
  status: 'active' | 'inactive';
  organizationId: string;
  createdAt: Date;
}

export interface Branch {
  id: string;
  name: string;
  address: string;
  contactPhone: string;
  status: 'active' | 'inactive';
  organizationId: string;
  createdAt: Date;
}

export interface Bank {
  id: string;
  bankName: string;
  accountNumber: string;
  ifscCode: string;
  branchName: string;
  accountHolderName: string;
  status: 'active' | 'inactive';
  organizationId: string;
  createdAt: Date;
}

export interface Language {
  id: string;
  name: string;
  isoCode: string;
  status: 'active' | 'inactive';
  organizationId: string;
  createdAt: Date;
}

export interface VitalType {
  id: string;
  name: string;
  unit: string;
  dataType: 'numeric' | 'numeric-range' | 'text' | 'boolean';
  status: 'active' | 'inactive';
  organizationId: string;
  createdAt: Date;
}

export interface Patient {
  id: string;
  firstName: string;
  lastName: string;
  fullName: string;
  age: number;
  dob?: string;
  gender: 'Male' | 'Female' | 'Other';
  contactPhone?: string;
  email?: string;
  organizationId: string;
  registrationDate: Date;
  createdAt?: Date;
  updatedAt?: Date;
}

export interface Doctor {
  id: string;
  firstName: string;
  lastName: string;
  specialty: string;
  designation: string;
  email: string;
  contactPhone?: string;
  departmentId: string;
  branchIds: string[];
  status: 'active' | 'inactive';
  organizationId: string;
  createdAt: Date;
}

export interface AuditLog {
  id: string;
  timestamp: Date;
  userId: string;
  userName: string;
  actionType: string;
  entityType: string;
  entityId: string;
  details: string;
  ipAddress: string;

  organizationId: string;
}

export interface Template {
  id: string;
  name: string;
  type: string;
  content: string;
  status: 'active' | 'inactive';
  organizationId: string;
  createdAt: Date;
}

export interface CreateUserForm {
  firstName: string;
  lastName: string;
  name: string;
  email: string;
  phone?: string;
  roleIds: string[];
  userRole: string;
  status: 'active' | 'inactive';
  organizationId: string;
}

export interface CreateOrganizationForm {
  name: string;
  contactPersonName: string;
  contactEmail: string;
  contactPhone?: string;
  address: {
    street1: string;
    city: string;
    state: string;
    postalCode: string;
    country: string;
  };
  description?: string;
}

export interface ApiResponse<T> {
  data: T;
  message: string;
  success: boolean;
}

export interface PaginatedResponse<T> {
  data: T[];
  total: number;
  page: number;
  limit: number;
}

export interface TableFilters {
  search?: string;
  status?: string;
  dateFrom?: Date;
  dateTo?: Date;
}

export interface SortConfig {
  field: string;
  direction: 'asc' | 'desc';
}

export interface AuthState {
  user: User | null;
  selectedOrganization: Organization | null;
  currentOrganization: Organization | null;
  isAuthenticated: boolean;
  isSuperAdmin: boolean;
  isOrganizationAdmin: boolean;
  isOrganizationUser: boolean;
  loading: boolean;
  error: string | null;
  successMessage: string | null;
  loggingOut: boolean;
  emrUserInfo: {
    id: string;
    email: string;
    name: string;
    [key: string]: any;
  } | null;
}

export type UserRole =
  | 'SUPER_ADMIN'
  | 'ORGANIZATION_ADMIN'
  | 'DOCTOR'
  | 'NURSE'
  | 'RECEPTIONIST';
