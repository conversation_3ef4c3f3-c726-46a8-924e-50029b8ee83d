import { Download, Plus, Trash2 } from 'lucide-react';
import React, { memo, useCallback, useEffect, useMemo, useState } from 'react';

import AppButton from '../../../components/Common/AppButton';
import ConfirmationModal from '../../../components/Common/ConfirmationModal';
import DataTable, { Column } from '../../../components/Common/DataTable';
import Modal from '../../../components/Common/Modal';
import SelectInput from '../../../components/Common/MUISelect';
import SearchBar from '../../../components/Common/SearchBar';
import StatusBadge from '../../../components/Common/StatusBadge';
import Tooltip from '../../../components/Common/Tooltip';
import { useToast } from '../../../contexts/ToastContext';
import { getCurrentOrganizationId } from '../../../utils/auth-utils';
import { useLabTests } from '../hooks/useLabTests';
import {
  fetchLabTestDepartments,
  getDepartmentFilterValue,
  LabTestDepartment,
} from '../services/department.service';
import {
  LabTestListItem,
  LabTestListParams,
  UpdateLabTestRequest,
} from '../types/labTest.types';
import AddLabTestForm from './AddLabTestForm';
import LabTestForm from './LabTestForm';

interface LabTestListProps {
  organizationName: string;
  organizationId?: string;
}

const LabTestList: React.FC<LabTestListProps> = memo(
  ({ organizationName, organizationId }) => {
    const currentOrgId = organizationId || getCurrentOrganizationId();
    const { warning } = useToast();

    const [showEditModal, setShowEditModal] = useState(false);
    const [showAddModal, setShowAddModal] = useState(false);
    const [editingTest, setEditingTest] = useState<LabTestListItem | null>(
      null
    );
    const [departments, setDepartments] = useState<LabTestDepartment[]>([]);
    const [loadingDepartments, setLoadingDepartments] = useState(true);

    const [showConfirmModal, setShowConfirmModal] = useState(false);
    const [isRemoving, setIsRemoving] = useState(false);
    const [isUpdating, setIsUpdating] = useState(false);

    // Track which operation initiated the bulk process
    const [bulkOperationType, setBulkOperationType] = useState<
      'update' | 'remove' | null
    >(null);

    const {
      tests,
      loading,
      creating,
      error: _error,
      updating,
      searchText,
      page,
      limit,
      total,
      departmentFilter,
      isActiveFilter,
      selectedTests,
      isAllSelected,
      totalPages: _totalPages,
      fetchLabTests: _fetchLabTests,
      createLabTest,
      updateLabTests,
      removeLabTests,
      exportLabTests,
      search,
      filterByDepartment,
      selectTest,
      selectAllTests,
      clearSelection: _clearSelection,
      clearMessages: _clearMessages,
      updateTestInList: _updateTestInList,
      hasSelection,
      selectedCount,
      bulkUpdate,
      hasMoreResults,
      setLoading,
    } = useLabTests(currentOrgId || undefined);

    const [localSearchText, setLocalSearchText] = useState(searchText);

    // Sync local search text with Redux search text
    useEffect(() => {
      setLocalSearchText(searchText);
    }, [searchText]);

    // Handle debounced search
    useEffect(() => {
      if (localSearchText === searchText) return;

      const timer = setTimeout(() => {
        search(localSearchText);
      }, 500);

      return () => clearTimeout(timer);
    }, [localSearchText, search, searchText]);

    const handleSearchInput = useCallback(
      (val: string) => {
        setLocalSearchText(val);
        // Show loading state immediately for better UX
        if (val !== searchText) {
          setLoading(true);
        }
      },
      [searchText, setLoading]
    );

    // Clear selection only when component unmounts, but preserve if bulk operation is in progress
    useEffect(() => {
      return () => {
        // Only clear selection if no bulk operation is in progress
        if (
          !bulkUpdate.isPolling ||
          (bulkUpdate.status?.status !== 'PROCESSING' &&
            bulkUpdate.status?.status !== 'PENDING')
        ) {
          _clearSelection();
        }
      };
    }, [_clearSelection, bulkUpdate.isPolling, bulkUpdate.status?.status]);

    // Check if bulk update is in progress
    const isBulkUpdateInProgress = updating || bulkUpdate.isPolling;

    // Enhanced loading states that persist throughout the entire operation
    const isRemoveInProgress =
      isRemoving ||
      (isBulkUpdateInProgress &&
        (bulkOperationType === 'remove' ||
          bulkUpdate.status?.type === 'LOINC_REMOVE'));
    const isUpdateInProgress =
      isUpdating ||
      (isBulkUpdateInProgress &&
        (bulkOperationType === 'update' ||
          bulkUpdate.status?.type === 'LOINC_UPDATE'));

    // Restore polling if component mounts and there's a pending job
    useEffect(() => {
      if (bulkUpdate.isPolling && bulkUpdate.status?.statusUrl) {
        import('../../../services/bulkUpdatePollingService').then(
          ({ bulkUpdatePollingService }) => {
            if (!bulkUpdatePollingService.isCurrentlyPolling()) {
              bulkUpdatePollingService.startLabTestsPolling(
                bulkUpdate.status!.statusUrl!
              );
            }
          }
        );
      }
    }, [bulkUpdate.isPolling, bulkUpdate.status?.statusUrl]);

    // Refresh list when bulk operation completes
    const lastCompletedJobId = React.useRef<string | null>(null);
    useEffect(() => {
      if (
        !isBulkUpdateInProgress &&
        bulkUpdate.status?.status === 'COMPLETED' &&
        bulkUpdate.status.id !== lastCompletedJobId.current
      ) {
        lastCompletedJobId.current = bulkUpdate.status.id;

        const params: Record<string, any> = {
          organizationId: currentOrgId,
          searchText,
          page,
          pageSize: limit,
          isActive:
            isActiveFilter === 'all' ? undefined : isActiveFilter === 'active',
        };
        if (departmentFilter && departmentFilter !== 'ALL') {
          params.department = departmentFilter;
        }
        _fetchLabTests(params as Partial<LabTestListParams>);
        _clearSelection();
      }
    }, [
      isBulkUpdateInProgress,
      bulkUpdate.status,
      _fetchLabTests,
      currentOrgId,
      searchText,
      page,
      limit,
      isActiveFilter,
      departmentFilter,
      _clearSelection,
    ]);

    // Reset operation type when bulk process completes
    useEffect(() => {
      if (!isBulkUpdateInProgress && bulkOperationType) {
        setBulkOperationType(null);
        setIsRemoving(false);
        setIsUpdating(false);
      }
    }, [isBulkUpdateInProgress, bulkOperationType]);

    // Load departments on mount
    React.useEffect(() => {
      const loadDepartments = async () => {
        try {
          setLoadingDepartments(true);
          const depts = await fetchLabTestDepartments();
          setDepartments(depts);
        } catch {
          // Failed to load departments, will use fallback
        } finally {
          setLoadingDepartments(false);
        }
      };

      loadDepartments();
    }, []);

    // Handle inline toggle for isActive - removed since status is now non-clickable

    // Handle save test (call API to update)
    const handleSaveTest = useCallback(
      async (
        testId: string,
        data: {
          displayName?: string | null;
          testName?: string;
          department: string;
          organizationCost: number;
          isActive: boolean;
        }
      ): Promise<void> => {
        if (!currentOrgId) return;

        try {
          // Prepare API data according to the new payload structure
          const updateData = {
            organizationId: currentOrgId,
            tests: [
              {
                testId,
                isActive: data.isActive,
                price: data.organizationCost,
                ...(data.testName ? { testName: data.testName } : {}),
                displayName: data.displayName || null,
                department: data.department,
              },
            ],
            suppressStatusIndicator: true,
          };

          await updateLabTests(updateData);
        } catch (error) {
          console.error('Error saving test:', error);
          throw error; // Re-throw to let the form handle the error
        }
      },
      [
        currentOrgId,
        updateLabTests,
        _fetchLabTests,
        searchText,
        departmentFilter,
        isActiveFilter,
        page,
        limit,
      ]
    );

    const handleCreateLabTest = useCallback(
      async (data: {
        testName: string;
        department: string;
        organizationCost: number | null;
        isActive: boolean;
      }) => {
        try {
          const processedData = {
            ...data,
            organizationCost: data.organizationCost ?? 0,
          };
          await createLabTest(processedData);
        } catch (error) {
          console.error('Error creating lab test:', error);
          throw error; // Re-throw to let the form handle the error
        }
      },
      [createLabTest]
    );

    // Handle bulk operations - directly call API
    const handleBulkUpdate = useCallback(async () => {
      if (!currentOrgId) return;

      if (isAllSelected && departmentFilter === 'ALL') {
        warning(
          'Department Required',
          'Please choose a specific department to perform bulk update on all tests.'
        );
        return;
      }

      setIsUpdating(true);
      setBulkOperationType('update');

      // For select all, we'll let the BulkUpdateStatusIndicator handle the polling
      // For individual selections, we'll refresh the list immediately after update
      const isSelectAllOperation = isAllSelected;

      try {
        const updateData: UpdateLabTestRequest = {
          organizationId: currentOrgId,
          tests: isSelectAllOperation
            ? []
            : selectedTests.map((testId: string) => {
                const test = tests.find((t) => t.testId === testId);
                return {
                  testId,
                  isActive: true,
                  price: test?.organizationCost || 0,
                  department: 'OTHERS',
                };
              }),
          selectAll: isSelectAllOperation,
          department: isSelectAllOperation
            ? getDepartmentFilterValue(departmentFilter)
            : '',
        };

        // Perform the update
        const result = await updateLabTests({
          ...updateData,
          // Only suppress status for individual selections
          suppressStatusIndicator: !isSelectAllOperation,
        });

        // If the operation completed synchronously (not async), reset the loading state
        // even for select all operations.
        const resultPayload = (result as any)?.payload;
        if (resultPayload && !resultPayload.async) {
          setIsUpdating(false);
          setBulkOperationType(null);

          // For select all sync operations, refresh the list manually since the Redux
          // logic might not trigger a full refresh for all pages automatically.
          if (isSelectAllOperation) {
            const params: Record<string, any> = {
              organizationId: currentOrgId,
              searchText,
              page,
              pageSize: limit,
              isActive:
                isActiveFilter === 'all'
                  ? undefined
                  : isActiveFilter === 'active',
            };

            if (departmentFilter && departmentFilter !== 'ALL') {
              params.department = departmentFilter;
            }

            await _fetchLabTests(params as Partial<LabTestListParams>);
          }
        }

        // For individual selections, refresh the list immediately
        if (!isSelectAllOperation) {
          const params: Record<string, any> = {
            organizationId: currentOrgId,
            searchText,
            page,
            pageSize: limit,
            isActive:
              isActiveFilter === 'all'
                ? undefined
                : isActiveFilter === 'active',
          };

          if (departmentFilter && departmentFilter !== 'ALL') {
            params.department = departmentFilter;
          }

          await _fetchLabTests(params as Partial<LabTestListParams>);
        }
      } catch (error) {
        console.error('Error updating lab tests:', error);
        setIsUpdating(false);
        setBulkOperationType(null);
      } finally {
        // Only keep loading if it's a select all operation AND it actually started an async job
        // (the Redux status will have polling true in that case)
        if (!isSelectAllOperation) {
          setIsUpdating(false);
          setBulkOperationType(null);
        }
      }
    }, [
      currentOrgId,
      departmentFilter,
      isActiveFilter,
      isAllSelected,
      selectedTests,
      updateLabTests,
      _clearSelection,
      _fetchLabTests,
      searchText,
      page,
      limit,
      warning,
    ]);

    // Handle remove operation
    const handleRemove = useCallback(async () => {
      if (!currentOrgId || (!selectedTests.length && !isAllSelected)) {
        return;
      }

      setIsRemoving(true);
      setBulkOperationType('remove');

      try {
        // Call the remove API (no parameters needed, it uses the current selection)
        const result = await removeLabTests();
        const isAsync = (result as any)?.async === true;

        if (!isAsync) {
          setIsRemoving(false);
          setBulkOperationType(null);
        }

        // If the removal was successful and synchronous, refresh the list
        if (
          !isAsync &&
          result &&
          typeof result === 'object' &&
          'success' in result &&
          result.success
        ) {
          const params: Record<string, any> = {
            organizationId: currentOrgId,
            searchText,
            page,
            pageSize: limit,
            isActive:
              isActiveFilter === 'all'
                ? undefined
                : isActiveFilter === 'active',
          };

          if (departmentFilter && departmentFilter !== 'ALL') {
            params.department = departmentFilter;
          }

          await _fetchLabTests(params as Partial<LabTestListParams>);
        }

        // Close the confirmation modal
        setShowConfirmModal(false);
      } catch (error) {
        console.error('Error removing lab tests:', error);
        setIsRemoving(false);
        setBulkOperationType(null);
      } finally {
        // Only keep loading if it's a select all operation AND it actually started an async job
        if (!isAllSelected) {
          setIsRemoving(false);
          setBulkOperationType(null);
        }
      }
    }, [
      currentOrgId,
      selectedTests,
      isAllSelected,
      departmentFilter,
      isActiveFilter,
      removeLabTests,
      _fetchLabTests,
      searchText,
      page,
      limit,
    ]);

    const confirmRemove = useCallback(() => {
      if (isAllSelected && departmentFilter === 'ALL') {
        warning(
          'Department Required',
          'Please choose a specific department to remove all tests.'
        );
        return;
      }
      setShowConfirmModal(true);
    }, [setShowConfirmModal, isAllSelected, departmentFilter, warning]);

    const cancelRemove = useCallback(() => {
      setShowConfirmModal(false);
    }, [setShowConfirmModal]);

    // Handle mark all tests (across all pages)
    const handleMarkAll = useCallback(() => {
      selectAllTests();
    }, [selectAllTests]);

    // Handle export
    const handleExport = useCallback(() => {
      exportLabTests();
    }, [exportLabTests]);

    // Handle search
    const handleSearchChange = useCallback(
      (searchTerm: string) => {
        handleSearchInput(searchTerm);
      },
      [handleSearchInput]
    );

    // Handle department filter
    const handleDepartmentFilterChange = useCallback(
      (department: string) => {
        // Store the original selected value in Redux, not the transformed value
        filterByDepartment(department);
      },
      [filterByDepartment]
    );

    // Handle status filter
    // const handleStatusFilterChange = useCallback(
    //   (status: string) => {
    //     filterByStatus(status);
    //   },
    //   [filterByStatus]
    // );

    // Pagination handler for new API
    const handlePageChange = useCallback(
      (newPage: number) => {
        // Always use the page number directly for navigation
        _fetchLabTests({ page: newPage });
      },
      [_fetchLabTests]
    );

    // Calculate counts of active and inactive selected items
    const { activeCount, inactiveCount } = useMemo(() => {
      if (isAllSelected) {
        // If all are selected, we use the total count but refine it based on current filters
        const effectiveCount = selectedCount || tests.length;

        if (isActiveFilter === 'active') {
          return { activeCount: effectiveCount, inactiveCount: 0 };
        } else if (isActiveFilter === 'inactive') {
          return { activeCount: 0, inactiveCount: effectiveCount };
        }

        // If 'all' status is filtered, we assume a mix exists
        return { activeCount: effectiveCount, inactiveCount: effectiveCount };
      }

      // Calculate counts based on current selection for individual pages
      const active = tests.filter(
        (test: LabTestListItem) =>
          selectedTests.includes(test.testId) && test.isActive
      ).length;
      const inactive = tests.filter(
        (test: LabTestListItem) =>
          selectedTests.includes(test.testId) && !test.isActive
      ).length;

      // Also consider selected tests that might not be on the current page
      // Use the total selected count but distribute it based on known patterns if needed
      // For simplicity, if we have selections, we assume they match the intent of the button
      const totalSelected = selectedTests.length;

      return {
        activeCount:
          active || (totalSelected > 0 && inactive === 0 ? totalSelected : 0),
        inactiveCount:
          inactive || (totalSelected > 0 && active === 0 ? totalSelected : 0),
      };
    }, [selectedTests, tests, isAllSelected, selectedCount, isActiveFilter]);

    // Handle selection of a single test
    const handleSelectTest = useCallback(
      (testId: string, selected: boolean) => {
        // The selectTest action will handle adding/removing the test ID
        // based on the selected parameter
        if (selected) {
          selectTest(testId);
        } else {
          // If unchecking, we need to remove the test ID from the selection
          selectTest(testId); // The Redux action will handle toggling
        }
      },
      [selectTest]
    );

    // Removed auto-selection of active tests to allow manual selection

    // Memoized columns definition
    const columns: Column<LabTestListItem>[] = useMemo(
      () => [
        {
          key: 'testName',
          label: 'Test Name',
          render: (_, test) => (
            <div style={{ maxWidth: 200 }}>
              <div className='text-sm font-medium text-gray-900'>
                {test.testName || ''}
              </div>
              <Tooltip content={test.shortName || ''}>
                <div
                  className='text-xs text-gray-500 cursor-help'
                  style={{
                    maxWidth: 250,
                    whiteSpace: 'nowrap',
                    overflow: 'hidden',
                    textOverflow: 'ellipsis',
                    display: 'block',
                  }}
                >
                  {test.shortName || ''}
                </div>
              </Tooltip>
            </div>
          ),
          width: '300px',
        },
        {
          key: 'displayName',
          label: 'Display Name',
          render: (_, test) => (
            <div className='text-sm text-gray-900'>
              {test.displayName || '-'}
            </div>
          ),
          width: '300px',
        },
        {
          key: 'defaultCost',
          label: 'Cost',
          render: (_, test) => (
            <span className='text-sm font-medium text-gray-900'>
              ₹{(test.defaultCost || 0).toFixed(2)}
            </span>
          ),
          width: '180px',
        },
        {
          key: 'organizationPrice',
          label: 'Organization Price',
          render: (_, test) => (
            <span className='text-sm font-medium text-blue-600'>
              {test.organizationPrice !== undefined &&
              test.organizationPrice !== null
                ? `₹${test.organizationPrice.toFixed(2)}`
                : '-'}
            </span>
          ),
        },
        {
          key: 'departments',
          label: 'Department',
          render: (_, test) => (
            <div className='flex flex-wrap gap-1'>
              {test.class ? (
                <span className='inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800'>
                  {test.class}
                </span>
              ) : (
                <span className='text-sm text-gray-500'>None</span>
              )}
            </div>
          ),
        },
        {
          key: 'isActive',
          label: 'Status',
          render: (_, test) => (
            <StatusBadge
              status={test.isActive ? 'active' : 'inactive'}
              size='sm'
            />
          ),
        },
      ],
      [
        selectedTests,
        handleSelectTest,
        handleMarkAll,
        isAllSelected,
        tests.length,
      ]
    );

    return (
      <div className='space-y-6'>
        {/* <BulkUpdateStatusIndicator show={showBulkUpdateStatus} /> */}

        {/* Confirmation Modal */}
        <ConfirmationModal
          isOpen={showConfirmModal}
          onClose={cancelRemove}
          onConfirm={handleRemove}
          title='Confirm Removal'
          message={
            isAllSelected
              ? 'Are you sure you want to remove all selected lab tests? This action cannot be undone.'
              : `Are you sure you want to remove ${selectedCount} selected lab test(s)?`
          }
          confirmText='Remove'
          cancelText='Cancel'
          type='danger'
          loading={isRemoveInProgress}
        />

        <div className='flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4'>
          <div>
            <h1 className='text-2xl font-bold text-gray-900'>Lab Tests</h1>
            <p className='mt-1 text-sm text-gray-500'>
              Manage lab tests for {organizationName}
            </p>
          </div>
          <div className='flex items-center space-x-3'>
            <AppButton
              onClick={() => setShowAddModal(true)}
              kind='primary'
              disabled={loading}
            >
              <Plus className='w-4 h-4 mr-2' /> New Test
            </AppButton>
            <SearchBar
              value={localSearchText}
              onChange={handleSearchChange}
              placeholder='Search lab tests...'
              size='md'
            />
            {hasSelection && (
              <>
                <AppButton
                  onClick={handleBulkUpdate}
                  disabled={
                    !hasSelection ||
                    loading ||
                    isUpdateInProgress ||
                    isRemoveInProgress ||
                    inactiveCount === 0
                  }
                  kind='primary'
                  startIcon={
                    isUpdateInProgress ? (
                      <div className='w-4 h-4 border-2 border-gray-600 border-t-transparent rounded-full animate-spin' />
                    ) : null
                  }
                  style={{
                    color: isUpdateInProgress ? '#333333' : undefined,
                  }}
                >
                  {isUpdateInProgress
                    ? `Updating... `
                    : `Bulk Update${!isAllSelected && inactiveCount > 0 ? ` (${inactiveCount})` : ''}`}
                </AppButton>
                <AppButton
                  onClick={confirmRemove}
                  disabled={
                    !hasSelection ||
                    loading ||
                    isRemoveInProgress ||
                    isUpdateInProgress ||
                    activeCount === 0
                  }
                  kind='primary'
                  startIcon={
                    isRemoveInProgress ? (
                      <div className='w-4 h-4 border-2 border-gray-600 border-t-transparent rounded-full animate-spin' />
                    ) : (
                      <Trash2 className='w-4 h-4' />
                    )
                  }
                  style={{
                    backgroundColor:
                      !hasSelection ||
                      loading ||
                      isRemoveInProgress ||
                      isUpdateInProgress ||
                      activeCount === 0
                        ? '#D3D3D3'
                        : '#dc2626',
                    color: isRemoveInProgress ? '#333333' : undefined,
                  }}
                >
                  {isRemoveInProgress
                    ? `Removing... `
                    : `Remove${!isAllSelected && activeCount > 0 ? ` (${activeCount})` : ''}`}
                </AppButton>
              </>
            )}

            {/* Temporarily hidden Import Excel button */}
            {/* eslint-disable-next-line no-constant-binary-expression */}
            {false && (
              <AppButton
                onClick={handleExport}
                disabled={loading}
                kind='secondary'
                startIcon={<Download className='w-4 h-4' />}
                size='small'
              >
                Import Excel
              </AppButton>
            )}
          </div>
        </div>

        {/* Data Table */}
        <DataTable<LabTestListItem>
          columns={columns}
          data={tests}
          loading={loading}
          selectedIds={selectedTests}
          isAllSelected={isAllSelected || isBulkUpdateInProgress}
          disableSelectAll={isBulkUpdateInProgress}
          onSelectAll={selectAllTests}
          onSelectOne={handleSelectTest}
          searchFilters={
            <div className='flex items-center space-x-3'>
              <div className='relative'>
                <SelectInput
                  value={loadingDepartments ? '' : departmentFilter || ''}
                  onChange={(e) => handleDepartmentFilterChange(e.target.value)}
                  disabled={loadingDepartments}
                  placeholder='Filter by Department'
                  options={
                    loadingDepartments
                      ? [{ label: 'Loading departments...', value: '' }]
                      : departments.map((dept) => ({
                          label: dept.label,
                          value: dept.value,
                        }))
                  }
                  size='small'
                  sx={{
                    minWidth: 220,
                  }}
                />
              </div>
            </div>
          }
          pagination={{
            total,
            page,
            limit,
            onPageChange: handlePageChange,
            variant: 'simple',
            hasMoreResults,
          }}
          onEdit={(test) => {
            setEditingTest(test);
            setShowEditModal(true);
          }}
        />

        {/* Edit Modal */}
        <Modal
          isOpen={showEditModal}
          onClose={() => !updating && setShowEditModal(false)}
          title='Edit Lab Test'
          size='md'
        >
          <div className='px-4 pb-4'>
            <LabTestForm
              test={editingTest}
              onSubmit={handleSaveTest}
              onSuccess={() => {
                setShowEditModal(false);
                setEditingTest(null);
              }}
              onClose={() => !updating && setShowEditModal(false)}
              updating={updating}
            />
          </div>
        </Modal>

        {/* Add Lab Test Modal */}
        <Modal
          isOpen={showAddModal}
          onClose={() => !creating && setShowAddModal(false)}
          title='Add Lab Test'
          size='md'
        >
          <div className='px-4 pb-4'>
            <AddLabTestForm
              onSubmit={handleCreateLabTest}
              onSuccess={() => {
                setShowAddModal(false);
              }}
              onClose={() => !creating && setShowAddModal(false)}
              submitting={creating}
            />
          </div>
        </Modal>
      </div>
    );
  }
);

LabTestList.displayName = 'LabTestList';

export default LabTestList;
