import MenuItem from '@mui/material/MenuItem';
import { OutlinedInputProps } from '@mui/material/OutlinedInput';
import TextField, { TextFieldProps } from '@mui/material/TextField';
import React, { useState } from 'react';

interface Option {
  label: string;
  value: string;
  disabled?: boolean;
}

type SelectInputProps = {
  label?: string;
  placeholder?: string;
  options: Option[];
  notchOnShrinkOnly?: boolean;
  forceLabelShrink?: boolean;
} & Omit<TextFieldProps, 'select'>;

const SelectInput = React.forwardRef<HTMLDivElement, SelectInputProps>(
  (
    {
      label,
      placeholder,
      options,
      notchOnShrinkOnly = false,
      forceLabelShrink = false,
      onFocus,
      onBlur,
      InputLabelProps,
      InputProps,
      ...props
    },
    ref
  ) => {
    const [isFocused, setIsFocused] = useState(false);

    // Decide shrink and notched only if the special behavior is enabled
    const shrinkVal = notchOnShrinkOnly
      ? (InputLabelProps?.shrink ?? (Boolean(props.value) || isFocused))
      : undefined;
    const notchedVal = notchOnShrinkOnly
      ? ((InputProps as unknown as Partial<OutlinedInputProps>)?.notched ??
        (Boolean(props.value) || isFocused))
      : undefined;

    const baseInputLabelProps: TextFieldProps['InputLabelProps'] = {
      ...(placeholder && !forceLabelShrink ? { shrink: false } : {}),
      ...InputLabelProps,
    };

    if (forceLabelShrink) {
      baseInputLabelProps.shrink = true;
    }

    const baseInputProps = {
      ...(InputProps as object),
    } as Partial<OutlinedInputProps>;

    if (notchOnShrinkOnly) {
      baseInputLabelProps.shrink = forceLabelShrink || !!shrinkVal;
      baseInputProps.notched = forceLabelShrink || !!notchedVal;
    }

    return (
      <TextField
        select
        fullWidth
        size='small'
        label={label}
        variant='outlined'
        inputRef={ref}
        {...props}
        onFocus={(e) => {
          setIsFocused(true);
          onFocus?.(e);
        }}
        onBlur={(e) => {
          setIsFocused(false);
          onBlur?.(e);
        }}
        InputLabelProps={baseInputLabelProps}
        InputProps={baseInputProps}
        SelectProps={{
          displayEmpty: true,
          onKeyDown: props.onKeyDown as any,
          renderValue: (selected) => {
            const option = options.find((opt) => opt.value === selected);

            if (option) {
              return option.label;
            }
            if (placeholder) {
              return <span style={{ color: '#a0a0a0' }}>{placeholder}</span>;
            }

            return '';
          },
          ...props.SelectProps,
        }}
        sx={{
          '& .MuiOutlinedInput-root': {
            fontSize: '0.875rem',
            paddingRight: 0,
          },
          '& .MuiInputBase-input': {
            paddingY: 1.2,
          },
          ...props.sx,
        }}
      >
        {options.map((option) => (
          <MenuItem
            key={option.value}
            value={option.value}
            disabled={Boolean(option.disabled)}
          >
            {option.label}
          </MenuItem>
        ))}
      </TextField>
    );
  }
);

SelectInput.displayName = 'SelectInput';

export default SelectInput;
