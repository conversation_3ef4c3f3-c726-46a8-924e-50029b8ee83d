/**
 * Unified Authentication Utilities
 * Provides a unified interface to work with both B2C and Cognito authentication
 */

import {
  clearCognitoTokens,
  cognitoLogout,
  getCognitoAccessToken,
  getCognitoUserInfo,
  isCognitoAuthenticated,
} from './cognitoAuth';
import { cognitoStorageKeys } from './cognitoConfig';
// B2C imports commented out - using only Cognito
// import {
//   getAccessToken as getB2CToken,
//   isAuthenticated as isB2CAuthenticated,
//   logout as b2cLogout,
// } from './msal';

// export type AuthProvider = 'b2c' | 'cognito' | null;
export type AuthProvider = 'cognito' | null;

/**
 * Get the current authentication provider
 */
export function getAuthProvider(): AuthProvider {
  const provider = sessionStorage.getItem(cognitoStorageKeys.authProvider);

  // Force Cognito if environment variables are present
  const cognitoClientId =
    (typeof window !== 'undefined'
      ? (window as any)._env_?.VITE_COGNITO_CLIENT_ID
      : '') ||
    import.meta.env.VITE_COGNITO_CLIENT_ID ||
    '';
  const cognitoDomain =
    (typeof window !== 'undefined'
      ? (window as any)._env_?.VITE_COGNITO_DOMAIN
      : '') ||
    import.meta.env.VITE_COGNITO_DOMAIN ||
    '';

  if (cognitoClientId && cognitoDomain) {
    return 'cognito';
  }

  if (provider === 'cognito') {
    return 'cognito';
  }

  // B2C check commented out - using only Cognito
  // if (isB2CAuthenticated()) {
  //   return 'b2c';
  // }

  return null;
}

/**
 * Check if user is authenticated with any provider
 */
export async function isAuthenticated(): Promise<boolean> {
  // B2C auth check commented out - using only Cognito
  return await isCognitoAuthenticated(); // || isB2CAuthenticated();
}

/**
 * Get access token from the active provider
 */
export async function getAccessToken(): Promise<string | null> {
  const provider = getAuthProvider();

  if (provider === 'cognito') {
    return await getCognitoAccessToken();
  }

  return null;
}

/**
 * Logout from the active provider
 */
export async function logout(): Promise<void> {
  const provider = getAuthProvider();

  if (provider === 'cognito') {
    await cognitoLogout();
  } else {
    // Clear everything just in case
    await clearCognitoTokens();
    localStorage.clear();
    sessionStorage.clear();
    window.location.href = '/login';
  }
}

/**
 * Get user email from the active provider
 */
export async function getUserEmail(): Promise<string | null> {
  const provider = getAuthProvider();

  if (provider === 'cognito') {
    const userInfo = await getCognitoUserInfo();
    return userInfo?.email || null;
  }

  return null;
}

/**
 * Switch authentication provider (for testing/development)
 */
export async function switchAuthProvider(provider: 'cognito'): Promise<void> {
  // Clear current auth state
  await clearCognitoTokens();
  sessionStorage.clear();
  localStorage.removeItem('token');

  // Redirect to appropriate login page
  if (provider === 'cognito') {
    window.location.href = '/cognito-login';
  } else {
    window.location.href = '/cognito-login';
  }
}

/**
 * Get authentication status for both providers
 */
export async function getAuthStatus(): Promise<{
  provider: AuthProvider;
  isAuthenticated: boolean;
  cognitoAuthenticated: boolean;
}> {
  const provider = getAuthProvider();
  const auth = await isAuthenticated();
  const cognitoAuth = await isCognitoAuthenticated();

  return {
    provider,
    isAuthenticated: auth,
    cognitoAuthenticated: cognitoAuth,
  };
}
