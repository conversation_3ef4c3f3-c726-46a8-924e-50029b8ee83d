/**
 * Cognito Authentication Hook
 * Custom React hook for Cognito authentication state and actions
 * Similar to useAuth but specifically for Cognito
 */

import { useEffect, useState } from 'react';

import cognitoAuthService, {
  CognitoUserInfo,
  EmrUserInfo,
} from '../store/features/auth/cognitoAuth.service';
import {
  clearCognitoTokens,
  cognitoLogin,
  cognitoLogout,
  getCognitoUserInfo,
  handleCognitoCallback,
  isCognitoAuthenticated,
  refreshCognitoToken,
} from '../utils/cognitoAuth';

interface UseCognitoAuthReturn {
  // State
  isAuthenticated: boolean;
  isLoading: boolean;
  cognitoUser: CognitoUserInfo | null;
  emrUser: EmrUserInfo | null;
  error: string | null;

  // Actions
  login: () => Promise<void>;
  logout: () => Promise<void>;
  refreshToken: () => Promise<boolean>;

  // User info
  getUserEmail: () => string | null;
  getDisplayName: () => string;
}

export const useCognitoAuth = (): UseCognitoAuthReturn => {
  const [isAuthenticated, setIsAuthenticated] = useState<boolean>(false);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [cognitoUser, setCognitoUser] = useState<CognitoUserInfo | null>(null);
  const [emrUser, setEmrUser] = useState<EmrUserInfo | null>(null);
  const [error, setError] = useState<string | null>(null);

  // Initialize authentication state on mount
  useEffect(() => {
    const initAuth = async () => {
      try {
        setIsLoading(true);
        setError(null);

        // Check if this is an OAuth callback
        const isCallback = window.location.search.includes('code=');

        if (isCallback) {
          console.log('Handling Cognito OAuth callback...');
          const success = await handleCognitoCallback();

          if (success) {
            console.log('Cognito callback handled successfully');
            // After successful callback, fetch user info
            await loadUserInfo();
          }
        } else if (await isCognitoAuthenticated()) {
          // User is already authenticated, load their info
          await loadUserInfo();
        } else {
          // Not authenticated
          setIsAuthenticated(false);
        }
      } catch (err: any) {
        console.error('Auth initialization error:', err);
        setError(err.message || 'Authentication failed');
        setIsAuthenticated(false);
        await clearCognitoTokens();
      } finally {
        setIsLoading(false);
      }
    };

    initAuth();
  }, []);

  // Load user information from Cognito and EMR
  const loadUserInfo = async () => {
    try {
      // Get Cognito user info
      const cognitoUserInfo = await getCognitoUserInfo();

      if (!cognitoUserInfo) {
        throw new Error('No Cognito user info available');
      }

      setCognitoUser(cognitoUserInfo);

      // Fetch EMR user info using the email from Cognito
      const email = cognitoUserInfo.email;

      if (email) {
        try {
          const { user: emrUserInfo } =
            await cognitoAuthService.fetchEmrUserInfoWithCognito(email);
          setEmrUser(emrUserInfo);
          setIsAuthenticated(true);
          console.log('User info loaded successfully');
        } catch (emrError: any) {
          console.error('Failed to fetch EMR user info:', emrError);
          // Still set authenticated if we have Cognito user
          setIsAuthenticated(true);
          setError('Failed to load complete user profile');
        }
      } else {
        setIsAuthenticated(true);
      }
    } catch (err: any) {
      console.error('Failed to load user info:', err);
      throw err;
    }
  };

  // Login action
  const login = async () => {
    try {
      setIsLoading(true);
      setError(null);
      await cognitoLogin();
    } catch (err: any) {
      console.error('Login error:', err);
      setError(err.message || 'Login failed');
      throw err;
    } finally {
      setIsLoading(false);
    }
  };

  // Logout action
  const logout = async () => {
    try {
      setIsLoading(true);
      setError(null);

      // Clear local state
      setIsAuthenticated(false);
      setCognitoUser(null);
      setEmrUser(null);

      // Perform Cognito logout (will redirect)
      await cognitoLogout();
    } catch (err: any) {
      console.error('Logout error:', err);
      setError(err.message || 'Logout failed');

      // Even if logout fails, clear local state and redirect
      await clearCognitoTokens();
      window.location.href = '/login';
    } finally {
      setIsLoading(false);
    }
  };

  // Refresh token action
  const refreshToken = async (): Promise<boolean> => {
    try {
      const success = await refreshCognitoToken();

      if (success) {
        // Reload user info after token refresh
        await loadUserInfo();
      }

      return success;
    } catch (err: any) {
      console.error('Token refresh error:', err);
      setError(err.message || 'Token refresh failed');
      return false;
    }
  };

  // Get user email
  const getUserEmail = (): string | null => {
    return cognitoUser?.email || emrUser?.email || null;
  };

  // Get display name
  const getDisplayName = (): string => {
    if (emrUser?.name) {
      return emrUser.name;
    }

    if (cognitoUser?.name) {
      return cognitoUser.name;
    }

    if (cognitoUser?.given_name && cognitoUser?.family_name) {
      return `${cognitoUser.given_name} ${cognitoUser.family_name}`;
    }

    if (cognitoUser?.email) {
      const emailParts = cognitoUser.email.split('@');
      return emailParts[0] || 'User';
    }

    return 'User';
  };

  return {
    // State
    isAuthenticated,
    isLoading,
    cognitoUser,
    emrUser,
    error,

    // Actions
    login,
    logout,
    refreshToken,

    // User info
    getUserEmail,
    getDisplayName,
  };
};
