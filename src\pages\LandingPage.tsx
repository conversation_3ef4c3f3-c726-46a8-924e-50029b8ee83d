import React, { useEffect, useState } from 'react';
import { useDispatch } from 'react-redux';
import { useNavigate } from 'react-router-dom';
import { toast } from 'react-toastify';

import LoadingSpinner from '../components/Common/LoadingSpinner';
import { PATHS } from '../constants/paths';
import { isAllowedAdminRole, ROLES } from '../constants/roles';
import { setAuthState, setLoading } from '../store/features/auth/auth.slice';
import userService from '../store/features/users/user.service';
import {
  cognitoLogin,
  getCognitoUserInfo,
  handleCognitoCallback,
  isCognitoAuthenticated,
} from '../utils/cognitoAuth';
import { mapEmrUserToUser } from '../utils/userMapping';

const LandingPage: React.FC = (): JSX.Element => {
  const navigate = useNavigate();
  const dispatch = useDispatch();

  const [isCheckingAuth, setIsCheckingAuth] = useState(() => {
    const urlParams = new URLSearchParams(window.location.search);
    const hasCode = urlParams.has('code');
    const isProcessing = Boolean(
      sessionStorage.getItem('cognito_processing_callback')
    );
    return hasCode || isProcessing;
  });

  useEffect(() => {
    const checkAuth = async () => {
      let shouldStopLoading = true;
      try {
        // Check if this is an OAuth callback from Cognito
        const urlParams = new URLSearchParams(window.location.search);
        const hasCode = urlParams.has('code');

        if (hasCode) {
          // Set global loading state immediately to prevent flicker in other components
          dispatch(setLoading(true));

          // Check if we are already processing this code in this session to prevent double invocation
          if (sessionStorage.getItem('cognito_processing_callback')) {
            console.log('Already processing callback, skipping...');
            // Keep the loader visible while the other invocation finishes processing.
            shouldStopLoading = false;
            return;
          }

          // Set flag to tell CognitoAuthHandler to wait
          sessionStorage.setItem('cognito_processing_callback', 'true');

          // Handle Cognito callback
          console.log('Handling Cognito OAuth callback...');
          try {
            console.log('LandingPage: Calling handleCognitoCallback...');
            const success = await handleCognitoCallback();
            console.log('LandingPage: handleCognitoCallback result:', success);
            if (success) {
              console.log(
                'Cognito authentication successful, fetching user profile...'
              );
              dispatch(setLoading(true));
              const cognitoUser = await getCognitoUserInfo();

              if (cognitoUser?.email) {
                // Fetch full user details from EMR API
                const emrUser = await userService.fetchUserByEmail(
                  cognitoUser.email
                );

                if (emrUser) {
                  const user = mapEmrUserToUser(emrUser);
                  const isSuperAdmin = user.roles.some(
                    (r: any) => r.name === ROLES.SUPER_ADMIN
                  );
                  const isAllowed = isAllowedAdminRole(
                    user.roles[0]?.name || ''
                  );

                  if (!isAllowed) {
                    console.log('User role not allowed:', user.roles[0]?.name);
                    sessionStorage.removeItem('cognito_processing_callback');
                    dispatch(setLoading(false));
                    navigate('/unauthorized', { replace: true });
                    return;
                  }

                  // Persist user to sessionStorage
                  sessionStorage.setItem('user', JSON.stringify(user));

                  // Update Redux state
                  dispatch(
                    setAuthState({
                      user,
                      selectedOrganization: null,
                      currentOrganization: null,
                    })
                  );

                  // DONE PROCESSING - Clear flags
                  sessionStorage.removeItem('preLoginUrl');
                  sessionStorage.removeItem('cognito_processing_callback');

                  // --- REDIRECT LOGIC ---
                  if (isSuperAdmin) {
                    console.log(
                      'User is Super Admin, redirecting to Organizations'
                    );
                    navigate(PATHS.ORGANIZATIONS, { replace: true });
                  } else {
                    console.log(
                      'User is Admin/Organization Admin, redirecting to Users'
                    );
                    navigate(PATHS.USERS, { replace: true });
                  }
                  console.log(
                    'User authorized, clearing flags and redirecting...'
                  );
                  dispatch(setLoading(false));
                  return;
                } else {
                  console.error(
                    'LandingPage: Fetch user by email returned no user - redirecting to unauthorized'
                  );
                  toast.error('User profile not found.');
                  sessionStorage.removeItem('cognito_processing_callback');
                  dispatch(setLoading(false));
                  navigate('/unauthorized');
                }
              } else {
                console.error(
                  'No email found in Cognito user info:',
                  cognitoUser
                );
                toast.error('Authentication error: Email missing.');
                sessionStorage.removeItem('cognito_processing_callback'); // Clear flag
                dispatch(setLoading(false));
                navigate('/login');
              }
            }
          } catch (callbackError) {
            dispatch(setLoading(false));
            console.error('Cognito callback error:', callbackError);
            toast.error('Authentication failed. Please try again.');
            sessionStorage.removeItem('cognito_processing_callback'); // Clear flag
            navigate('/login');
          }
        } else if (await isCognitoAuthenticated()) {
          console.log(
            'User already authenticated, ensuring profile is loaded...'
          );
          const cognitoUser = await getCognitoUserInfo();

          if (cognitoUser?.email) {
            // Check if user is already in Redux or sessionStorage
            const storedUser = sessionStorage.getItem('user');
            if (!storedUser) {
              try {
                dispatch(setLoading(true));
                const emrUser = await userService.fetchUserByEmail(
                  cognitoUser.email
                );
                if (emrUser) {
                  const user = mapEmrUserToUser(emrUser);
                  sessionStorage.setItem('user', JSON.stringify(user));
                  dispatch(setAuthState({ user, selectedOrganization: null }));
                }
              } catch (e) {
                console.warn('Silent profile fetch failed:', e);
              } finally {
                dispatch(setLoading(false));
              }
            }
          }

          const redirectUrl =
            sessionStorage.getItem('preLoginUrl') || PATHS.ROOT;
          sessionStorage.removeItem('preLoginUrl');
          navigate(redirectUrl);
        }
      } catch (error) {
        console.error('Error checking authentication status:', error);
        toast.error('An error occurred during authentication check.');
      } finally {
        if (shouldStopLoading) {
          setIsCheckingAuth(false);
        }
      }
    };

    checkAuth();
  }, [navigate, dispatch]);

  // Show loading state while checking auth status
  if (isCheckingAuth) {
    return (
      <div className='flex flex-col items-center justify-center min-h-screen bg-gray-50'>
        <LoadingSpinner size='lg' text='Completing sign in...' />
      </div>
    );
  }

  const handleLogin = async () => {
    try {
      // Check if already authenticated to prevent multiple login attempts
      const authenticated = await isCognitoAuthenticated();
      if (authenticated) {
        console.log('LandingPage: User already authenticated, redirecting...');
        const preLoginUrl = sessionStorage.getItem('preLoginUrl');
        if (
          preLoginUrl &&
          preLoginUrl !== PATHS.ROOT &&
          preLoginUrl !== PATHS.LOGIN
        ) {
          sessionStorage.removeItem('preLoginUrl');
          navigate(preLoginUrl);
        } else {
          // Fallback to role-based default redirect
          // Note: handlePostLoginRedirect resets currentSessionHandled, so it will work even if called multiple times
          const { handlePostLoginRedirect, resetRedirectState } = await import(
            '../utils/redirect-utils'
          );
          resetRedirectState();
          handlePostLoginRedirect(navigate, window.location.pathname);
        }
        return;
      }

      // Remove any pre-set redirect to ensure clean slate
      sessionStorage.removeItem('preLoginUrl');

      // Initiate Cognito login
      console.log('LandingPage: Calling cognitoLogin...');
      await cognitoLogin();
    } catch (error) {
      console.error('LandingPage: Login error:', error);
      const errorMessage =
        error instanceof Error ? error.message : 'Unknown error';
      toast.error(`Failed to initiate login: ${errorMessage}`);
    }
  };

  return (
    <div className='flex min-h-screen bg-white'>
      {/* Left side with blue pattern */}
      <div className='hidden md:flex md:w-4/6 items-center justify-center bg-[#f0f7ff] relative overflow-hidden'>
        <div className="absolute inset-0 bg-[url('/images/auth-background.png')] bg-cover bg-center"></div>
        <div className='relative z-10 text-center p-8'>
          <img
            src='/images/auth-logo.png'
            alt='ARCA HEALTH SPHERE'
            className='h-34 w-auto mx-auto mb-6'
          />
        </div>
      </div>

      {/* Right side with form */}
      <div className='flex-1 flex flex-col justify-center items-center p-8 max-w-md mx-auto'>
        <div className='w-full space-y-8'>
          <div className='text-center space-y-2'>
            <h2 className='text-3xl font-bold text-gray-900'>
              Welcome to ArcaAI Admin
            </h2>
            <p className='text-gray-500 text-sm'>Sign in to continue</p>
          </div>

          <div className='mt-8 flex flex-col items-center'>
            <button
              onClick={handleLogin}
              className='w-full flex justify-center items-center px-6 py-2 border border-transparent rounded-md shadow-sm text-base font-medium text-white bg-black hover:bg-gray-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 disabled:opacity-50 disabled:cursor-not-allowed transition-colors'
            >
              Sign In
            </button>
            <p className='mt-4 text-sm text-gray-600'>
              Don't have an account?{' '}
              <span
                onClick={() => {
                  const baseUrl =
                    (typeof window !== 'undefined'
                      ? (window as any)._env_?.VITE_EMR_URL
                      : '') ||
                    import.meta.env.VITE_EMR_URL ||
                    '';
                  window.location.href = `${baseUrl}/subscription/signup`;
                }}
                className='font-semibold text-black cursor-pointer hover:opacity-80'
              >
                Signup
              </span>
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default LandingPage;
