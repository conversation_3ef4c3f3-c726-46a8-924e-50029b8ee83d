import { Amplify } from 'aws-amplify';
import { cognitoUserPoolsTokenProvider } from 'aws-amplify/auth/cognito';

import { cognitoConfig } from '../../../utils/cognitoConfig';
import { amplifySessionStorage } from '../storage/amplify-session-storage';

export const configureAmplify = () => {
  if (typeof window === 'undefined') return;

  Amplify.configure({
    Auth: {
      Cognito: {
        userPoolId: cognitoConfig.userPoolId,
        userPoolClientId: cognitoConfig.clientId,
        // we omit loginWith.oauth here to use manual control if needed
      },
    },
  });

  // Set the storage provider
  cognitoUserPoolsTokenProvider.setKeyValueStorage(amplifySessionStorage);
};
