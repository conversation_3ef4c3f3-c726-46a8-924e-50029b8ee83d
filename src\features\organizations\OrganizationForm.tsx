﻿import { yupResolver } from '@hookform/resolvers/yup';
import React, { useEffect } from 'react';
import { Controller, useForm } from 'react-hook-form';
import { useDispatch } from 'react-redux';

import AppButton from '../../components/Common/AppButton';
import TextInput from '../../components/Common/MUIInput';
import { AppDispatch } from '../../store';
import {
  createOrganization,
  updateOrganization,
} from '../../store/features/organizations/organization.slice';
import { Organization } from '../../types';
import { formatNumericInput, handleNumericInput } from '../../utils/inputUtils';
import {
  OrganizationFormData,
  organizationSchema,
} from './validation/organizationSchema';

interface OrganizationFormProps {
  organization?: Organization | null;
  onSuccess: () => void;
  onCancel: () => void;
}

const OrganizationForm: React.FC<OrganizationFormProps> = ({
  organization,
  onSuccess,
  onCancel,
}) => {
  const dispatch = useDispatch<AppDispatch>();
  const defaultValues: OrganizationFormData = {
    name: '',
    contactPersonName: '',
    contactEmail: '',
    contactPhone: '+91',
    address: {
      street1: '',
      city: '',
      state: '',
      postalCode: '',
      country: '',
    },
    description: null,
    registrationFee: null,
    isClinicOrganization: false,
  };

  const {
    control,
    handleSubmit,
    reset,
    setFocus,
    formState: { errors, isSubmitting },
  } = useForm<OrganizationFormData>({
    resolver: yupResolver(organizationSchema) as any, // Type assertion to handle yup schema type
    defaultValues,
    // mode: 'onChange',
  });

  const formatPhoneNumber = (value: string): string => {
    const digits = value.replace(/\D/g, '');

    if (value.startsWith('+91')) {
      const afterPrefix = value.substring(3).replace(/\D/g, '');

      const limitedDigits = afterPrefix.substring(0, 10);
      return `+91${limitedDigits}`;
    }

    if (digits.startsWith('91') && digits.length > 2) {
      const afterCountryCode = digits.substring(2).substring(0, 10);
      return `+91${afterCountryCode}`;
    }

    const limitedDigits = digits.substring(0, 10);
    return `+91${limitedDigits}`;
  };

  useEffect(() => {
    if (organization) {
      reset({
        name: organization.name,
        contactPersonName: organization.contactPersonName,
        contactEmail: organization.contactEmail,
        contactPhone: organization.contactPhone
          ? formatPhoneNumber(organization.contactPhone)
          : '+91',
        address: {
          street1: organization.address.street,
          city: organization.address.city,
          state: organization.address.state,
          postalCode: organization.address.postalCode,
          country: organization.address.country,
        },
        description: organization.description || null,
        registrationFee: organization.registrationFee ?? null,
        isClinicOrganization: organization.isClinicOrganization ?? false,
      });
    }
  }, [organization, reset]);

  const onSubmit = async (data: OrganizationFormData) => {
    try {
      const formattedPhone = data.contactPhone?.trim() || '';
      const finalPhone = formattedPhone.startsWith('+91')
        ? formattedPhone
        : `+91${formattedPhone.replace(/\D/g, '').substring(0, 10)}`;

      const shouldIncludePhone = finalPhone !== '+91' && finalPhone.length > 3;

      const payload = {
        name: data.name,
        contactPersonName: data.contactPersonName,
        contactEmail: data.contactEmail,
        contactPhone: shouldIncludePhone ? finalPhone : '',
        address: {
          street: data.address.street1,
          city: data.address.city,
          state: data.address.state,
          postalCode: data.address.postalCode,
          country: data.address.country,
        },
        description: data.description || '',
        registrationFee: data.registrationFee ?? null,
        isClinicOrganization: data.isClinicOrganization ?? false,
      };
      let result;
      if (organization) {
        result = await dispatch(
          updateOrganization({ ...payload, id: organization?.id || null })
        );
      } else {
        result = await dispatch(createOrganization(payload));
      }
      if (result && result.type && result.type.endsWith('fulfilled')) {
        onSuccess();
      }
    } catch {
      // Error is intentionally not used
    }
  };

  const formFields: (keyof OrganizationFormData | string)[] = [
    'name',
    'contactPersonName',
    'contactEmail',
    'contactPhone',
    'registrationFee',
    'isClinicOrganization',
    'address.street1',
    'address.city',
    'address.state',
    'address.postalCode',
    'address.country',
    'description',
  ];

  const handleKeyDown = (
    e: React.KeyboardEvent,
    currentIndex: number,
    isLastField: boolean
  ) => {
    if (e.key === 'Enter') {
      e.preventDefault();
      if (isLastField) {
        handleSubmit(onSubmit)();
      } else {
        const nextField = formFields[currentIndex + 1];
        setFocus(nextField as any);
      }
    }
  };

  return (
    <div className='space-y-6 p-6'>
      <form onSubmit={handleSubmit(onSubmit)} className='space-y-4'>
        <Controller
          name='name'
          control={control}
          render={({ field }) => (
            <TextInput
              {...field}
              label='Organization Name *'
              error={!!errors.name}
              helperText={errors.name?.message}
              placeholder='Enter organization name'
              onKeyDown={(e) => handleKeyDown(e, 0, false)}
            />
          )}
        />
        <Controller
          name='contactPersonName'
          control={control}
          render={({ field }) => (
            <TextInput
              {...field}
              label='Contact Person *'
              error={!!errors.contactPersonName}
              helperText={errors.contactPersonName?.message}
              placeholder='Enter contact person name'
              onKeyDown={(e) => handleKeyDown(e, 1, false)}
            />
          )}
        />
        <Controller
          name='contactEmail'
          control={control}
          render={({ field }) => (
            <TextInput
              {...field}
              label='Contact Email *'
              error={!!errors.contactEmail}
              helperText={errors.contactEmail?.message}
              placeholder='Enter contact email'
              onKeyDown={(e) => handleKeyDown(e, 2, false)}
            />
          )}
        />
        <Controller
          name='contactPhone'
          control={control}
          render={({ field: { onChange, value, ...restField } }) => (
            <TextInput
              {...restField}
              value={value || '+91'}
              label='Contact Phone'
              error={!!errors.contactPhone}
              helperText={errors.contactPhone?.message}
              placeholder='+91 1234567890'
              type='tel'
              onChange={(e) => {
                const inputValue = e.target.value;

                if (inputValue.length < 3 || !inputValue.startsWith('+91')) {
                  if (inputValue.length < 3) {
                    onChange('+91');
                  } else {
                    const digits = inputValue
                      .replace(/\D/g, '')
                      .substring(0, 10);
                    onChange(`+91${digits}`);
                  }
                } else {
                  const afterPrefix = inputValue.substring(3);
                  const digits = afterPrefix
                    .replace(/\D/g, '')
                    .substring(0, 10);
                  onChange(`+91${digits}`);
                }
              }}
              onKeyDown={(e) => handleKeyDown(e, 3, false)}
            />
          )}
        />
        <Controller
          name='registrationFee'
          control={control}
          render={({ field: { onChange, value, ...restField } }) => (
            <TextInput
              {...restField}
              value={value ?? ''}
              label='Registration Fee *'
              error={!!errors.registrationFee}
              helperText={errors.registrationFee?.message}
              placeholder='Enter registration fee'
              inputProps={{
                inputMode: 'decimal',
              }}
              onKeyDown={(e) => {
                handleNumericInput(e as any);
                handleKeyDown(e, 4, false);
              }}
              onChange={(e) => {
                const formattedValue = formatNumericInput(e.target.value);
                if (formattedValue === '') {
                  onChange(null);
                } else {
                  const numericValue = parseFloat(formattedValue);
                  // Don't allow negative numbers
                  if (numericValue < 0) {
                    return;
                  }
                  onChange(isNaN(numericValue) ? 0 : numericValue);
                }
              }}
            />
          )}
        />
        <Controller
          name='isClinicOrganization'
          control={control}
          render={({ field }) => (
            <div className='flex items-center space-x-2'>
              <input
                type='checkbox'
                id='isClinicOrganization'
                checked={field.value || false}
                onChange={(e) => field.onChange(e.target.checked)}
                className='h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded'
              />
              <label
                htmlFor='isClinicOrganization'
                className='text-sm font-medium text-gray-700'
              >
                Is Clinic Organization
              </label>
            </div>
          )}
        />
        {/* Address Fields */}
        <div className='border-t pt-6'>
          <h3 className='text-lg font-medium text-gray-900 mb-4'>
            Address Information
          </h3>
          <div className='space-y-4'>
            <Controller
              name='address.street1'
              control={control}
              render={({ field }) => (
                <TextInput
                  {...field}
                  label='Street Address *'
                  error={!!errors.address?.street1}
                  helperText={errors.address?.street1?.message}
                  placeholder='Enter street address'
                  multiline
                  minRows={1}
                  maxRows={4}
                  onKeyDown={(e) => handleKeyDown(e, 6, false)}
                />
              )}
            />
            <Controller
              name='address.city'
              control={control}
              render={({ field }) => (
                <TextInput
                  {...field}
                  label='City *'
                  error={!!errors.address?.city}
                  helperText={errors.address?.city?.message}
                  placeholder='Enter city'
                  onKeyDown={(e) => handleKeyDown(e, 7, false)}
                />
              )}
            />
            <Controller
              name='address.state'
              control={control}
              render={({ field }) => (
                <TextInput
                  {...field}
                  label='State *'
                  error={!!errors.address?.state}
                  helperText={errors.address?.state?.message}
                  placeholder='Enter state'
                  onKeyDown={(e) => handleKeyDown(e, 8, false)}
                />
              )}
            />
            <Controller
              name='address.postalCode'
              control={control}
              render={({ field: { onChange, value, ...restField } }) => (
                <TextInput
                  {...restField}
                  value={value || ''}
                  label='Postal Code *'
                  error={!!errors.address?.postalCode}
                  helperText={
                    errors.address?.postalCode?.message ||
                    'Enter 6-digit postal code'
                  }
                  placeholder='Enter 6-digit postal code'
                  type='tel'
                  inputProps={{
                    maxLength: 6,
                    inputMode: 'numeric',
                    pattern: '\\d{0,6}',
                  }}
                  onKeyPress={(e) => {
                    // Only allow numbers (0-9)
                    if (!/[0-9]/.test(e.key)) {
                      e.preventDefault();
                    }
                  }}
                  onChange={(e) => {
                    // Remove any non-digit characters and limit to 6 digits
                    const newValue = e.target.value
                      .replace(/\D/g, '')
                      .slice(0, 6);
                    onChange(newValue);
                  }}
                  onKeyDown={(e) => handleKeyDown(e, 9, false)}
                />
              )}
            />
            <Controller
              name='address.country'
              control={control}
              render={({ field }) => (
                <TextInput
                  {...field}
                  label='Country *'
                  error={!!errors.address?.country}
                  helperText={errors.address?.country?.message}
                  placeholder='Enter country'
                  onKeyDown={(e) => handleKeyDown(e, 10, false)}
                />
              )}
            />
          </div>
        </div>
        {/* Description Field */}
        <Controller
          name='description'
          control={control}
          render={({ field }) => (
            <TextInput
              {...field}
              label='Description'
              error={!!errors.description}
              helperText={errors.description?.message}
              placeholder='Enter description'
              multiline
              rows={3}
              onKeyDown={(e) => handleKeyDown(e, 11, true)}
            />
          )}
        />
        {/* Form Actions */}
        <div className='flex justify-end space-x-4 pt-4'>
          <AppButton
            type='button'
            kind='secondary'
            onClick={onCancel}
            disabled={isSubmitting}
          >
            Cancel
          </AppButton>
          <AppButton type='submit' kind='primary' disabled={isSubmitting}>
            {isSubmitting
              ? 'Saving...'
              : organization
                ? 'Save Changes'
                : 'Create Organization'}
          </AppButton>
        </div>
      </form>
    </div>
  );
};

export default OrganizationForm;
