import {
    AccountInfo,
    AuthenticationResult,
    Configuration,
    EventMessage,
    EventType,
    InteractionRequiredAuthError,
    LogLevel,
    PublicClientApplication,
    RedirectRequest,
    SilentRequest,
} from '@azure/msal-browser';

import { b2cPolicies, defaultScopes, msalConfig } from './msalConfig';

// Application routes
export const PATHS = {
  LOGIN: '/login',
  DASHBOARD: '/dashboard',
  WELCOME: '/welcome',
};

// MSAL configuration
export const msalInstanceConfig: Configuration = {
  ...msalConfig,
  system: {
    loggerOptions: {
      loggerCallback: (
        level: LogLevel,
        message: string,
        containsPii: boolean
      ) => {
        if (containsPii) {
          return;
        }
        switch (level) {
          case LogLevel.Error:
            console.error(message);
            return;
          case LogLevel.Info:
            console.info(message);
            return;
          case LogLevel.Verbose:
            console.debug(message);
            return;
          case LogLevel.Warning:
            console.warn(message);
            return;
          default:
            return;
        }
      },
      logLevel:
        process.env.NODE_ENV === 'development'
          ? LogLevel.Verbose
          : LogLevel.Error,
    },
    windowHashTimeout: 9000, // 9 seconds
    iframeHashTimeout: 9000,
    loadFrameTimeout: 9000,
  },
};

// Create and export MSAL instance
export const msalInstance = new PublicClientApplication(msalInstanceConfig);

// Type guard to check if the account is valid
const isValidAccount = (account: any): account is AccountInfo => {
  return account && typeof account === 'object' && 'homeAccountId' in account;
};

// Initialize active account if available
const initializeActiveAccount = () => {
  const accounts = msalInstance.getAllAccounts();
  if (accounts.length > 0) {
    const account = accounts[0];
    if (isValidAccount(account)) {
      msalInstance.setActiveAccount(account);
    }
  }
};

// Initialize active account on load
initializeActiveAccount();

// Add event callbacks for handling redirect responses
msalInstance.addEventCallback((event: EventMessage) => {
  try {
    switch (event.eventType) {
      case EventType.LOGIN_SUCCESS:
      case EventType.ACQUIRE_TOKEN_SUCCESS: {
        if (event.payload) {
          const payload = event.payload as AuthenticationResult;

          // Set the active account - this is important for single-account scenarios
          if (payload.account) {
            msalInstance.setActiveAccount(payload.account);

            // Store the tokens
            if (payload.accessToken) {
              const accessToken = payload.accessToken;
              sessionStorage.setItem('token', accessToken);
              sessionStorage.setItem('msal.accessToken', accessToken);
            }

            if (payload.idToken) {
              const idToken = payload.idToken;
              sessionStorage.setItem('msal.idToken', idToken);
            }

            // Store account info
            sessionStorage.setItem(
              'msal.account',
              JSON.stringify(payload.account)
            );
          }
        }
        break;
      }

      case EventType.LOGIN_FAILURE:
        console.error('Login failed:', event.error);
        // Redirect to login page on failure
        window.location.href = PATHS.LOGIN;
        break;

      case EventType.ACQUIRE_TOKEN_FAILURE:
        console.error('Failed to acquire token:', event.error);
        // Handle token acquisition failures (e.g., token expired)
        if (event.error instanceof InteractionRequiredAuthError) {
          console.warn('Interaction required to get new token');
          // Redirect to login if token acquisition fails
          window.location.href = PATHS.LOGIN;
        }
        break;

      case EventType.LOGOUT_END:
        // Clear all stored tokens and account info on logout
        sessionStorage.removeItem('msal.accessToken');
        sessionStorage.removeItem('msal.idToken');
        sessionStorage.removeItem('msal.account');
        sessionStorage.removeItem('msal.tokenExpires');
        break;
    }
  } catch (error) {
    console.error('Error in MSAL event callback:', error);
  }
});

// Helper function to get access token
export async function getAccessToken(): Promise<string> {
  try {
    // Try to get token from sessionStorage first
    const storedToken = sessionStorage.getItem('token');
    const account = msalInstance.getActiveAccount();

    if (storedToken) {
      return storedToken;
    }

    // If not in sessionStorage, try other key
    const sessionToken = sessionStorage.getItem('msal.accessToken');
    if (sessionToken) {
      // Move to correct key for consistency
      sessionStorage.setItem('token', sessionToken);
      return sessionToken;
    }

    // If no valid token found, try to get a new one silently
    if (!account) {
      console.warn('getAccessToken: No active account found');
      throw new Error('No active account');
    }

    try {
      const silentRequest: SilentRequest = {
        scopes: [...defaultScopes],
        account,
        forceRefresh: false,
      };

      const response = await msalInstance.acquireTokenSilent(silentRequest);

      if (response.accessToken) {
        // Store the token in sessionStorage for persistence
        sessionStorage.setItem('token', response.accessToken);
        sessionStorage.setItem('msal.accessToken', response.accessToken);

        if (response.idToken) {
          sessionStorage.setItem('msal.idToken', response.idToken);
        }

        return response.accessToken;
      }
      throw new Error('No access token in response');
    } catch (error) {
      console.error('Error acquiring token silently:', error);

      if (error instanceof InteractionRequiredAuthError) {
        // Store current URL for redirect back after login
        if (
          window.location.pathname &&
          !window.location.pathname.includes('/login')
        ) {
          sessionStorage.setItem('preLoginUrl', window.location.pathname);
        }
        // Clear any existing tokens
        sessionStorage.removeItem('token');
        sessionStorage.removeItem('msal.accessToken');
        // Redirect to login
        window.location.href = PATHS.LOGIN;
      }

      throw error; // Re-throw to be handled by the caller
    }
  } catch (error) {
    console.error('Error in getAccessToken:', error);
    throw error;
  }
}

// Helper function to check if user is authenticated
export const isAuthenticated = (): boolean => {
  const account = msalInstance.getActiveAccount();
  return !!account;
};

// Helper function to get the active account
export const getActiveAccount = (): AccountInfo | null => {
  const account = msalInstance.getActiveAccount();
  return account ? account : null;
};

// Helper function to handle login redirect
export const login = async (): Promise<void> => {
  try {
    // Store the current URL to redirect back after login
    const redirectUrl = window.location.href;
    sessionStorage.setItem('preLoginUrl', redirectUrl);

    // Clear any existing tokens
    sessionStorage.removeItem('token');
    sessionStorage.removeItem('msal.accessToken');
    sessionStorage.removeItem('msal.idToken');

    // Get the current account if it exists
    const account = msalInstance.getActiveAccount();

    // If we have an account, try to get a token silently first
    if (account) {
      try {
        const token = await getAccessToken();
        if (token) {
          return;
        }
      } catch (silentError) {
        // Intentionally ignore silent token acquisition errors and proceed to interactive login
      }
    }

    // Set up the login request with B2C policy
    const loginRequest: RedirectRequest = {
      scopes: [...defaultScopes], // Use the default scopes we defined
      redirectUri: window.location.origin,
      authority: b2cPolicies.authorities.signUpSignIn.authority,
      // Force account selection and prompt for credentials
      prompt: 'select_account',
      // Request an ID token and access token
      extraQueryParameters: {
        response_type: 'id_token token',
        scope: defaultScopes.join(' '), // Join scopes with space for the query parameter
      },
    };

    // Initiate login redirect
    await msalInstance.loginRedirect(loginRequest);
  } catch (error) {
    console.error('Login failed:', error);

    // If there's an interaction in progress error, clear storage and retry
    if (error instanceof Error) {
      if (error.message.includes('interaction_in_progress')) {
        sessionStorage.clear();
        window.location.reload();
      } else if (error.message.includes('AADB2C90091')) {
        // User cancelled the login
        return;
      }
    }

    throw error;
  }
};

// Helper function to handle logout
export async function logout(): Promise<void> {
  try {
    // Clear all auth-related storage
    sessionStorage.removeItem('token');
    sessionStorage.removeItem('user');

    // Clear MSAL storage
    sessionStorage.removeItem('msal.accessToken');
    sessionStorage.removeItem('msal.idToken');
    sessionStorage.removeItem('msal.account');

    // Clear cookies
    document.cookie.split(';').forEach((cookie) => {
      const [name] = cookie.trim().split('=');
      document.cookie = `${name}=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;`;
    });

    // Perform MSAL logout
    try {
      await msalInstance.logoutRedirect({
        postLogoutRedirectUri: `${window.location.origin}/login`,
      });
    } catch (msalError) {
      console.error('Logout: MSAL logout error:', msalError);
      // Even if MSAL logout fails, continue with our cleanup
    }

    // Force a full page reload to ensure all state is cleared
    window.location.href = '/login';
  } catch (error) {
    console.error('Logout: Error during logout:', error);
    // Even if there's an error, redirect to login
    window.location.href = '/login';
    throw error;
  }
}
