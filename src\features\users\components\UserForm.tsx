import React, { memo, useEffect, useState } from 'react';
import { Controller } from 'react-hook-form';

import AppButton from '../../../components/Common/AppButton';
import TextInput from '../../../components/Common/MUIInput';
import SelectInput from '../../../components/Common/MUISelect';
import { useAuth } from '../../../hooks/useAuth';
import {
  formatNumericInput,
  formatRoleName,
  handleNumericInput,
} from '../../../utils/inputUtils';
import { useRoles } from '../../roles/hooks/useRoles';
import { useUserForm } from '../hooks/useUserForm';
import { UserFormSchema } from '../schemas/user.schema';
import { User, USER_STATUSES } from '../types/user.types';

interface UserFormProps {
  user?: User | null;
  organizationId?: string | null | undefined;
  onSubmit: (data: UserFormSchema) => void | Promise<void>;
  onSuccess: () => void;
  onCancel: () => void;
}

const UserForm: React.FC<UserFormProps> = memo(
  ({ user, organizationId, onSubmit, onSuccess, onCancel }) => {
    const { selectedOrganization } = useAuth();
    const {
      roles,
      fetchRoles,
      loading: rolesLoading,
    } = useRoles(organizationId || undefined);
    const {
      control,
      handleSubmit: formSubmitHandler,
      errors,
      isSubmitting,
      isEditing,
      setValue,
      watch,
      setFocus,
    } = useUserForm({
      user: user || null,
      onSubmit,
      onSuccess,
    });

    const selectedRole = watch('userRole');
    const [submitAttempted, setSubmitAttempted] = useState(false);
    useEffect(() => {
      if (selectedRole && roles.length > 0) {
        const role = roles.find((r) => r.name === selectedRole);
        if (role) {
          setValue('roleId', role.id);
        }
      }
    }, [selectedRole, roles, setValue]);

    useEffect(() => {
      if (organizationId) {
        fetchRoles({}, true); // Fetch all roles for dropdown
      }
    }, [fetchRoles, organizationId]);

    useEffect(() => {
      if (!isEditing) {
        setValue('userRole', '');
        setValue('roleId', '');
        setValue('consultationFee', null);
      } else if (user) {
        setValue('userRole', user.userRole || '');
        setValue('roleId', user.roleId || '');
        setValue('consultationFee', user.consultationFee || null);
      }
    }, [user, isEditing, setValue]);

    useEffect(() => {
      if (selectedRole && roles.length > 0) {
        const role = roles.find((r) => r.name === selectedRole);
        if (role) {
          setValue('roleId', role.id);
        }
      } else if (!selectedRole) {
        setValue('roleId', '');
      }
    }, [selectedRole, roles, setValue]);

    useEffect(() => {
      if (isEditing && user?.userRole && roles.length > 0 && !rolesLoading) {
        const matchingRole = roles.find(
          (role) => role.name.toLowerCase() === user.userRole?.toLowerCase()
        );
        if (matchingRole) {
          setValue('userRole', matchingRole.name);
          setValue('roleId', matchingRole.id);
        }
      }
    }, [isEditing, user, roles, rolesLoading, setValue]);

    const formFields = [
      'name',
      'email',
      'userRole',
      'consultationFee',
      'status',
    ];

    const handleKeyDown = (
      e: React.KeyboardEvent,
      currentIndex: number,
      isLastField: boolean
    ) => {
      if (e.key === 'Enter') {
        e.preventDefault();
        if (isLastField) {
          formSubmitHandler(e as any);
        } else {
          const nextField = formFields[currentIndex + 1];
          setFocus(nextField as any);
        }
      }
    };

    return (
      <div className='space-y-6 p-6'>
        <form
          onSubmit={(e) => {
            setSubmitAttempted(true);
            formSubmitHandler(e);
          }}
          className='space-y-4'
        >
          <div>
            <Controller
              name='name'
              control={control}
              render={({ field }) => (
                <TextInput
                  {...field}
                  label='Name *'
                  error={submitAttempted && !!errors.name}
                  helperText={submitAttempted ? errors.name?.message : ''}
                  placeholder='Enter name'
                  onKeyDown={(e) => handleKeyDown(e, 0, false)}
                />
              )}
            />
          </div>
          <div>
            <Controller
              name='email'
              control={control}
              render={({ field }) => (
                <TextInput
                  {...field}
                  label='Email Address *'
                  error={submitAttempted && !!errors.email}
                  helperText={submitAttempted ? errors.email?.message : ''}
                  placeholder='Enter email address'
                  onKeyDown={(e) => handleKeyDown(e, 1, false)}
                />
              )}
            />
          </div>
          <div>
            <Controller
              name='userRole'
              control={control}
              render={({ field }) => (
                <SelectInput
                  {...field}
                  label={rolesLoading ? '' : 'User Role *'}
                  error={submitAttempted && !!errors.userRole}
                  helperText={submitAttempted ? errors.userRole?.message : ''}
                  disabled={rolesLoading}
                  options={
                    rolesLoading
                      ? // When loading, show user's current role if editing, otherwise show loading
                        user && user.userRole
                        ? [
                            {
                              label: formatRoleName(user.userRole),
                              value: user.userRole,
                            },
                          ]
                        : [{ label: 'Loading roles...', value: '' }]
                      : (() => {
                          const roleOptions = roles.map((role) => ({
                            label: formatRoleName(role.name),
                            value: role.name,
                          }));

                          if (user && user.userRole) {
                            const hasMatchingRole = roles.some(
                              (role) =>
                                role.name.toLowerCase() ===
                                  user.userRole?.toLowerCase() ||
                                role.name.toLowerCase().replace(/\s+/g, '_') ===
                                  user.userRole?.toLowerCase() ||
                                role.name.toLowerCase().replace(/_/g, ' ') ===
                                  user.userRole?.toLowerCase()
                            );

                            if (!hasMatchingRole) {
                              roleOptions.unshift({
                                label: formatRoleName(user.userRole),
                                value: user.userRole,
                              });
                            }
                          }

                          return roleOptions;
                        })()
                  }
                  onKeyDown={(e) => handleKeyDown(e, 2, false)}
                />
              )}
            />
          </div>

          <div>
            <Controller
              name='consultationFee'
              control={control}
              render={({ field: { onChange, value, ...restField } }) => {
                const isFeeDisabled =
                  selectedOrganization?.name === 'Subscription - Organization';

                return (
                  <TextInput
                    {...restField}
                    value={value ?? ''}
                    label='Consultation Fee'
                    error={submitAttempted && !!errors.consultationFee}
                    helperText={
                      submitAttempted ? errors.consultationFee?.message : ''
                    }
                    placeholder='Enter consultation fee'
                    disabled={isFeeDisabled}
                    inputProps={{
                      inputMode: 'decimal',
                    }}
                    onKeyDown={(e) => {
                      handleNumericInput(e as any);
                      handleKeyDown(e, 3, !isEditing);
                    }}
                    onChange={(e) => {
                      const formattedValue = formatNumericInput(e.target.value);
                      onChange(
                        formattedValue === ''
                          ? null
                          : parseFloat(formattedValue) || 0
                      );
                    }}
                    sx={isFeeDisabled ? { backgroundColor: '#f3f4f6' } : {}}
                  />
                );
              }}
            />
          </div>
          <div>
            <Controller
              name='status'
              control={control}
              render={({ field }) => (
                <SelectInput
                  {...field}
                  label='Status *'
                  error={submitAttempted && !!errors.status}
                  helperText={submitAttempted ? errors.status?.message : ''}
                  disabled={!isEditing}
                  options={USER_STATUSES.map((status) => ({
                    label: status.label,
                    value: status.value,
                  }))}
                  sx={!isEditing ? { backgroundColor: '#f3f4f6' } : {}}
                  onKeyDown={(e) => handleKeyDown(e, 4, true)}
                />
              )}
            />
          </div>
          <div className='flex justify-end space-x-4 pt-4'>
            <AppButton
              type='button'
              kind='secondary'
              onClick={onCancel}
              disabled={isSubmitting}
            >
              Cancel
            </AppButton>
            <AppButton type='submit' kind='primary'>
              {isSubmitting
                ? 'Saving...'
                : isEditing
                  ? 'Save Changes'
                  : 'Create User'}
            </AppButton>
          </div>
        </form>
      </div>
    );
  }
);

UserForm.displayName = 'UserForm';

export default UserForm;
