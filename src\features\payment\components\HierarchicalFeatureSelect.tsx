import ArrowDropDownIcon from '@mui/icons-material/ArrowDropDown';
import { InputAdornment, Menu, MenuItem, TextField } from '@mui/material';
import React, { useState } from 'react';

export type HierarchicalFeatureOption = {
  label: string;
  value: string;
  disabled?: boolean;
  children?: Array<{
    label: string;
    value: string;
    disabled?: boolean;
  }>;
};

interface HierarchicalFeatureSelectProps {
  placeholder?: string;
  value: string;
  onChange: (value: string) => void;
  onMultipleChange?: (values: string[]) => void;
  options: HierarchicalFeatureOption[];
  disabled?: boolean;
  sx?: object;
  selectedFeatures?: Set<string>;
  onKeyDown?: (e: React.KeyboardEvent) => void;
}

const HierarchicalFeatureSelect = React.forwardRef<
  HTMLDivElement,
  HierarchicalFeatureSelectProps
>(
  (
    {
      placeholder = 'Select',
      value,
      onChange,
      onMultipleChange,
      options,
      disabled = false,
      sx,
      selectedFeatures = new Set(),
      onKeyDown,
    },
    ref
  ) => {
    const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
    const [expandedParents, setExpandedParents] = useState<Set<string>>(
      new Set()
    );

    const handleOpen = (event: React.MouseEvent<HTMLElement>) => {
      if (disabled) return;
      setAnchorEl(event.currentTarget);
    };

    const handleClose = () => {
      setAnchorEl(null);
      setExpandedParents(new Set());
    };

    const handleParentToggle = (option: HierarchicalFeatureOption) => {
      if (option.value === '__ALL__') {
        onChange(option.value);
        return;
      }

      const isSelected =
        value === option.value || selectedFeatures.has(option.value);

      if (isSelected) {
        if (selectedFeatures.has(option.value)) {
          return;
        }

        onChange('');
      } else {
        onChange(option.value);
      }
    };

    const handleParentExpand = (option: HierarchicalFeatureOption) => {
      const newExpanded = new Set(expandedParents);
      if (newExpanded.has(option.value)) {
        newExpanded.delete(option.value);
      } else {
        newExpanded.add(option.value);
      }
      setExpandedParents(newExpanded);
    };

    const handleChildClick = (childValue: string) => {
      if (selectedFeatures.has(childValue)) {
        return;
      }

      const parentOption = options.find((opt) =>
        opt.children?.some((child) => child.value === childValue)
      );

      if (onMultipleChange) {
        const valuesToSelect: string[] = [];
        if (parentOption && parentOption.value !== '__ALL__') {
          valuesToSelect.push(parentOption.value);
        }
        valuesToSelect.push(childValue);
        onMultipleChange(valuesToSelect);
      } else {
        if (parentOption && parentOption.value !== '__ALL__') {
          onChange(parentOption.value);
        } else {
          onChange(childValue);
        }
        handleClose();
      }
    };

    const getDisplayValue = () => {
      if (!value) return '';
      if (value === '__ALL__') return 'All';

      const parentOption = options.find((opt) => opt.value === value);
      if (parentOption) {
        return parentOption.label;
      }

      for (const option of options) {
        if (option.children) {
          const childOption = option.children.find(
            (child) => child.value === value
          );
          if (childOption) {
            return childOption.label;
          }
        }
      }

      return '';
    };

    const isOpen = Boolean(anchorEl);

    return (
      <>
        <TextField
          fullWidth
          size='small'
          value={getDisplayValue()}
          onClick={handleOpen}
          placeholder={placeholder}
          disabled={disabled}
          inputRef={ref}
          onKeyDown={onKeyDown}
          sx={{
            '& .MuiOutlinedInput-root': {
              fontSize: '0.875rem',
              cursor: disabled ? 'not-allowed' : 'pointer',
            },
            '& .MuiInputBase-input': {
              paddingY: 1.2,
              cursor: disabled ? 'not-allowed' : 'pointer',
            },
            ...sx,
          }}
          InputProps={{
            readOnly: true,
            endAdornment: (
              <InputAdornment position='end'>
                <ArrowDropDownIcon
                  sx={{
                    color: disabled ? 'action.disabled' : 'action.active',
                    pointerEvents: 'none',
                  }}
                />
              </InputAdornment>
            ),
          }}
        />
        <Menu
          anchorEl={anchorEl}
          open={isOpen}
          onClose={handleClose}
          anchorOrigin={{
            vertical: 'bottom',
            horizontal: 'left',
          }}
          transformOrigin={{
            vertical: 'top',
            horizontal: 'left',
          }}
          PaperProps={{
            style: {
              maxHeight: 300,
              width: anchorEl
                ? `${anchorEl.getBoundingClientRect().width}px`
                : 'auto',
              minWidth: anchorEl
                ? `${anchorEl.getBoundingClientRect().width}px`
                : 'auto',
            },
          }}
        >
          {options.map((option) => {
            const isAllOption = option.value === '__ALL__';
            const isParentSelected =
              value === option.value || selectedFeatures.has(option.value);
            const isExpanded = expandedParents.has(option.value);
            const hasChildren =
              option.children && option.children.length > 0 && !isAllOption;
            const showCheckbox = !selectedFeatures.has(option.value);

            return (
              <React.Fragment key={option.value}>
                <MenuItem
                  onClick={(e) => {
                    e.stopPropagation();

                    if (isAllOption) {
                      handleClose();
                    }
                  }}
                  disabled={Boolean(option.disabled)}
                  sx={{
                    display: 'flex',
                    alignItems: 'center',
                    padding: '8px 16px',
                    backgroundColor:
                      isParentSelected && !showCheckbox
                        ? 'rgba(0, 0, 0, 0.04)'
                        : 'transparent',
                    '&:hover': {
                      backgroundColor: 'rgba(0, 0, 0, 0.04)',
                    },
                  }}
                >
                  {showCheckbox ? (
                    <input
                      type='checkbox'
                      checked={isParentSelected}
                      onChange={(e) => {
                        e.stopPropagation();
                        handleParentToggle(option);
                      }}
                      onClick={(e) => e.stopPropagation()}
                      style={{
                        marginRight: '12px',
                        cursor: 'pointer',
                        width: '16px',
                        height: '16px',
                      }}
                    />
                  ) : (
                    <input
                      type='checkbox'
                      checked={true}
                      readOnly
                      style={{
                        marginRight: '12px',
                        cursor: 'default',
                        width: '16px',
                        height: '16px',
                      }}
                    />
                  )}
                  <span style={{ flex: 1 }}>{option.label}</span>
                  {hasChildren && (
                    <span
                      onClick={(e) => {
                        e.stopPropagation();
                        handleParentExpand(option);
                      }}
                      style={{
                        marginLeft: '8px',
                        fontSize: '0.75rem',
                        cursor: 'pointer',
                        padding: '4px 8px',
                        userSelect: 'none',
                        transform: isExpanded
                          ? 'rotate(90deg)'
                          : 'rotate(0deg)',
                        transition: 'transform 0.2s',
                      }}
                    >
                      ▶
                    </span>
                  )}
                </MenuItem>
                {hasChildren && isExpanded && option.children && (
                  <>
                    {option.children.map((child) => {
                      const isChildSelected = selectedFeatures.has(child.value);
                      return (
                        <MenuItem
                          key={child.value}
                          onClick={(e) => {
                            e.stopPropagation();
                            if (!isChildSelected) {
                              handleChildClick(child.value);
                            }
                          }}
                          disabled={Boolean(child.disabled) || isChildSelected}
                          sx={{
                            paddingLeft: '48px',
                            paddingY: '6px',
                            fontSize: '0.875rem',
                            backgroundColor: isChildSelected
                              ? 'rgba(0, 0, 0, 0.04)'
                              : 'transparent',
                            opacity: isChildSelected ? 0.7 : 1,
                            '&:hover': {
                              backgroundColor: isChildSelected
                                ? 'rgba(0, 0, 0, 0.04)'
                                : 'rgba(0, 0, 0, 0.08)',
                            },
                          }}
                        >
                          <input
                            type='checkbox'
                            checked={isChildSelected}
                            readOnly
                            style={{
                              marginRight: '8px',
                              cursor: isChildSelected ? 'default' : 'pointer',
                              width: '14px',
                              height: '14px',
                            }}
                          />
                          <span>{child.label}</span>
                        </MenuItem>
                      );
                    })}
                  </>
                )}
              </React.Fragment>
            );
          })}
        </Menu>
      </>
    );
  }
);

export default HierarchicalFeatureSelect;
