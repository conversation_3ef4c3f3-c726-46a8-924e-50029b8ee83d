import { DOCTOR_PROFILE_ENDPOINT } from '../../../constants/api-endpoints';
import api from '../../../services/api';

export interface DoctorProfileQualification {
  degree?: string;
  specialization?: string;
  university?: string;
  institute?: string;
  yearOfCompletion?: string;
}

export interface DoctorProfileExperience {
  hospitalName?: string;
  designation?: string;
  duration?: {
    from?: string;
    to?: string;
  };
}

export interface DoctorProfileResponse {
  id?: string;
  username?: string;
  general?: {
    fullName?: string;
    designation?: string;
    department?: string;
    doctorID?: string;
    doctorId?: string;
    contactNumber?: string;
    workEmail?: string;
  };
  personal?: {
    age?: string;
    bloodGroup?: string;
    dateOfWedding?: string;
    maritalStatus?: string;
    birthDetails?: {
      placeOfBirth?: string;
      state?: string;
      district?: string;
      country?: string;
    };
    address?: unknown;
    dob?: string;
  };
  professionalDetails?: {
    qualifications?: DoctorProfileQualification[];
    certifications?: {
      name?: string;
      regNumber?: string;
      validFrom?: string;
      validTo?: string;
      status?: string;
    }[];
    experience?: DoctorProfileExperience[];
  };
}

const fetchDoctorProfileByEmail = async (email: string) => {
  const response = await api.get<DoctorProfileResponse>(
    DOCTOR_PROFILE_ENDPOINT,
    {
      params: { email },
    }
  );

  return response.data;
};

const doctorProfileService = {
  fetchDoctorProfileByEmail,
};

export default doctorProfileService;
