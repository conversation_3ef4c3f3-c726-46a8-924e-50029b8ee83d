import { toast } from 'react-toastify';

import authService from '../store/features/auth/auth.service';
import {
  clearCognitoTokens,
  cognitoLogin,
  cognitoLogout,
  getCognitoIdToken,
  getCognitoUserInfo,
  isCognitoAuthenticated,
} from './cognitoAuth';

export async function login() {
  try {
    await cognitoLogin();
  } catch (error) {
    console.error('Login error:', error);
    toast.error('<PERSON><PERSON> failed. Please try again.');
  }
}

export async function logout() {
  try {
    await cognitoLogout();
    // After logout, clear everything
    sessionStorage.clear();
    localStorage.clear();
  } catch (error) {
    console.error('Logout error:', error);
    toast.error('Logout failed');
  }
}

export async function getToken(): Promise<string> {
  try {
    // Check if authenticated with Cognito
    if (await isCognitoAuthenticated()) {
      const cognitoToken = await getCognitoIdToken();
      if (cognitoToken) {
        return cognitoToken;
      }
    }

    throw new Error('User is not authenticated with Cognito');
  } catch (error) {
    console.error('getToken: Error getting access token:', {
      error,
      message: error instanceof Error ? error.message : 'Unknown error',
    });
    throw error;
  }
}

export async function getActiveAccount() {
  try {
    const cognitoUserInfo = await getCognitoUserInfo();
    if (cognitoUserInfo) {
      return cognitoUserInfo;
    } else {
      throw new Error('Only Cognito authentication is supported');
    }
  } catch (error) {
    console.error('Error getting active account:', error);
    throw error;
  }
}

export async function fetchUserInfo() {
  try {
    const userInfo = await getActiveAccount();
    if (!userInfo?.email) {
      throw new Error('No active account found');
    }

    const { user, token } = await authService.fetchEmrUserInfo(userInfo.email);

    return {
      token,
      user,
    };
  } catch (error) {
    console.error('Error in fetchUserInfo:', error);

    // Clear local cache on error to force re-authentication if necessary
    sessionStorage.removeItem('user');
    localStorage.removeItem('user'); // Also remove from localStorage if any
    await clearCognitoTokens();

    throw error;
  }
}
