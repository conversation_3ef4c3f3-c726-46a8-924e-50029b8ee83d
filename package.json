{"name": "emr-admin-module", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "start": "serve -s dist -l 8080", "build": "npm run validate && vite build", "build:fast": "vite build", "build:strict": "npm run type-check && npm run lint && vite build", "validate": "npm run type-check && npm run lint", "validate:strict": "npm run type-check && npm run lint && npm run format:check", "lint": "eslint .", "lint:fix": "eslint . --fix", "format": "prettier --write .", "format:check": "prettier --check .", "type-check": "tsc --noEmit --pretty", "type-check:watch": "tsc --noEmit --watch --pretty", "preview": "vite preview"}, "dependencies": {"@azure/msal-browser": "^4.14.0", "@azure/msal-react": "^3.0.14", "@emotion/react": "^11.11.1", "@emotion/styled": "^11.11.0", "@grafana/faro-react": "^1.19.0", "@grafana/faro-web-sdk": "^1.19.0", "@grafana/faro-web-tracing": "^1.19.0", "@hookform/resolvers": "^5.1.1", "@mui/icons-material": "^5.15.3", "@mui/material": "^5.15.3", "@mui/x-data-grid": "^6.18.3", "@mui/x-date-pickers": "^6.18.3", "@reduxjs/toolkit": "^2.0.1", "@types/react-datepicker": "^6.2.0", "@xstate/react": "^4.1.0", "antd": "^5.26.1", "aws-amplify": "^6.15.9", "axios": "^1.10.0", "date-fns": "^3.6.0", "lucide-react": "^0.344.0", "react": "^18.3.1", "react-datepicker": "^8.4.0", "react-dom": "^18.3.1", "react-error-boundary": "^6.0.0", "react-hook-form": "^7.58.0", "react-hot-toast": "^2.5.2", "react-icons": "^5.5.0", "react-quill": "^2.0.0", "react-redux": "^9.0.4", "react-router-dom": "^6.20.1", "react-toastify": "^11.0.5", "serve": "^14.2.4", "uuid": "^9.0.1", "xstate": "^5.5.0", "yup": "^1.6.1"}, "devDependencies": {"@eslint/js": "^9.9.1", "@types/node": "^24.0.3", "@types/react": "^18.3.5", "@types/react-dom": "^18.3.0", "@types/uuid": "^9.0.7", "@vitejs/plugin-react": "^4.3.1", "autoprefixer": "^10.4.18", "eslint": "^9.9.1", "eslint-config-prettier": "^10.1.5", "eslint-plugin-import": "^2.31.0", "eslint-plugin-prettier": "^5.5.0", "eslint-plugin-react-hooks": "^5.1.0-rc.0", "eslint-plugin-react-refresh": "^0.4.11", "eslint-plugin-simple-import-sort": "^12.1.1", "eslint-plugin-unused-imports": "^4.1.4", "globals": "^15.9.0", "postcss": "^8.4.35", "tailwindcss": "^3.4.1", "typescript": "^5.5.3", "typescript-eslint": "^8.3.0", "vite": "^5.4.2", "vite-plugin-checker": "^0.9.3", "vite-plugin-static-copy": "^3.1.1"}, "lint-staged": {"*.{ts,tsx}": ["eslint --fix", "prettier --write", "bash -c 'npm run type-check'"], "*.{js,jsx}": ["eslint --fix", "prettier --write"], "*.{json,css,scss,md}": ["prettier --write"], "src/**/*.{ts,tsx}": ["bash -c 'npm run type-check'"]}}