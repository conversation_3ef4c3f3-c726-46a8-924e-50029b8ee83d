/**
 * Amazon Cognito Configuration
 * This file contains all Cognito-related configuration for AWS authentication
 */

// Cognito Configuration - Environment-based configuration
export const cognitoConfig = {
  // Your Cognito User Pool ID
  userPoolId:
    (typeof window !== 'undefined'
      ? (window as any)._env_?.VITE_COGNITO_USER_POOL_ID
      : '') ||
    import.meta.env.VITE_COGNITO_USER_POOL_ID ||
    '',

  // Your Cognito Region
  region:
    (typeof window !== 'undefined'
      ? (window as any)._env_?.VITE_COGNITO_REGION
      : '') ||
    import.meta.env.VITE_COGNITO_REGION ||
    // Try to infer region from domain (e.g. ....ap-south-1.amazoncognito.com)
    (
      (typeof window !== 'undefined'
        ? (window as any)._env_?.VITE_COGNITO_DOMAIN
        : '') ||
      import.meta.env.VITE_COGNITO_DOMAIN ||
      ''
    ).match(/\.([a-z0-9-]+)\.amazoncognito\.com/)?.[1] ||
    'us-east-1',

  // Your Cognito User Pool Client ID
  clientId:
    (typeof window !== 'undefined'
      ? (window as any)._env_?.VITE_COGNITO_CLIENT_ID
      : '') ||
    import.meta.env.VITE_COGNITO_CLIENT_ID ||
    '',

  // Your Cognito Domain (without https://)
  cognitoDomain:
    (typeof window !== 'undefined'
      ? (window as any)._env_?.VITE_COGNITO_DOMAIN
      : '') ||
    import.meta.env.VITE_COGNITO_DOMAIN ||
    '',

  // Your application's redirect URI (must be registered in Cognito)
  redirectUri:
    (typeof window !== 'undefined'
      ? (window as any)._env_?.VITE_COGNITO_REDIRECT_URI
      : '') ||
    import.meta.env.VITE_COGNITO_REDIRECT_URI ||
    (typeof window !== 'undefined'
      ? window.location.origin
      : 'http://localhost:3000'),

  // Post logout redirect URI
  postLogoutRedirectUri:
    (typeof window !== 'undefined'
      ? (window as any)._env_?.VITE_COGNITO_POST_LOGOUT_REDIRECT_URI
      : '') ||
    import.meta.env.VITE_COGNITO_POST_LOGOUT_REDIRECT_URI ||
    (typeof window !== 'undefined'
      ? `${window.location.origin}/login`
      : 'http://localhost:3000/login'),

  // OAuth scopes to request
  scopes: ['openid', 'email', 'profile'],

  // Response type for OAuth flow
  responseType: 'code',

  // PKCE challenge method
  codeChallengeMethod: 'S256',
} as const;

// Cognito endpoints
export const cognitoEndpoints = {
  authorize: `https://${cognitoConfig.cognitoDomain}/oauth2/authorize`,
  token: `https://${cognitoConfig.cognitoDomain}/oauth2/token`,
  logout: `https://${cognitoConfig.cognitoDomain}/logout`,
  userInfo: `https://${cognitoConfig.cognitoDomain}/oauth2/userInfo`,
} as const;

// Storage keys for Cognito
export const cognitoStorageKeys = {
  accessToken: 'cognito.accessToken',
  idToken: 'cognito.idToken',
  refreshToken: 'cognito.refreshToken',
  tokenExpiry: 'cognito.tokenExpiry',
  userInfo: 'cognito.userInfo',
  codeVerifier: 'cognito.codeVerifier',
  state: 'cognito.state',
  authProvider: 'authProvider', // 'b2c' or 'cognito'
} as const;
