import React, { useMemo } from 'react';
import { useNavigate } from 'react-router-dom';

import Modal from '../../../components/Common/Modal';
import { PATHS } from '../../../constants/paths';
import { useAuth } from '../../../hooks/useAuth';
import { DoctorProfileResponse } from '../../../services/doctorProfile.service';
import { InvoiceDoctorInfo } from '../../../store/features/invoices/invoice.service';
import { capitalizeFirstLetter } from '../../../utils/inputUtils';

interface DoctorProfileModalProps {
  isOpen: boolean;
  onClose: () => void;
  doctorProfile: DoctorProfileResponse | null;
  profilePictureUrl: string | null | undefined;
  invoiceDoctor: InvoiceDoctorInfo | null | undefined;
  loading?: boolean;
}

const DoctorProfileModal: React.FC<DoctorProfileModalProps> = ({
  isOpen,
  onClose,
  doctorProfile,
  profilePictureUrl,
  invoiceDoctor,
  loading = false,
}) => {
  const navigate = useNavigate();
  const { isSuperAdmin } = useAuth();

  const general = doctorProfile?.general;
  const personal = doctorProfile?.personal;
  const professional = doctorProfile?.professionalDetails;

  const formatDate = (value?: string): string => {
    if (!value) return '-';
    const date = new Date(value);
    if (Number.isNaN(date.getTime())) {
      return value.split('T')[0] || value;
    }
    return date.toLocaleDateString('en-IN', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
    });
  };

  const computeExperience = useMemo(() => {
    const experience = professional?.experience;
    if (!experience || experience.length === 0) {
      return '-';
    }

    let totalMonths = 0;
    experience.forEach((item) => {
      const from = item.duration?.from ? new Date(item.duration.from) : null;
      const to = item.duration?.to ? new Date(item.duration.to) : new Date();

      if (!from || Number.isNaN(from.getTime())) return;
      if (!to || Number.isNaN(to.getTime())) return;

      const months =
        (to.getFullYear() - from.getFullYear()) * 12 +
        (to.getMonth() - from.getMonth());
      if (months > 0) {
        totalMonths += months;
      }
    });

    if (totalMonths <= 0) return '-';
    const years = Math.floor(totalMonths / 12);
    const remainingMonths = totalMonths % 12;

    if (years > 0 && remainingMonths > 0) {
      return `${years} yrs ${remainingMonths} mos`;
    }
    if (years > 0) return `${years} yrs`;
    return `${remainingMonths} mos`;
  }, [professional?.experience]);

  const qualifications = useMemo(() => {
    const items = professional?.qualifications;
    if (!items || items.length === 0) return '-';

    return items
      .map((q) => {
        const parts = [q.degree, q.specialization].filter(Boolean);
        return parts.join(', ');
      })
      .join(' | ');
  }, [professional?.qualifications]);

  const doctorName =
    general?.fullName || invoiceDoctor?.name || doctorProfile?.username || '-';
  const doctorAge = personal?.age || '-';
  const doctorDob =
    personal?.dob ||
    personal?.birthDetails?.dateOfBirth ||
    personal?.birthDetails?.placeOfBirth ||
    '-';
  const doctorId = general?.doctorId || general?.doctorID || '-';
  const department = general?.department || invoiceDoctor?.department || '-';

  const dateOfJoining = doctorProfile?.created_on
    ? formatDate(doctorProfile.created_on)
    : '-';

  return (
    <Modal isOpen={isOpen} onClose={onClose} title='Doctor Profile' size='md'>
      {loading ? (
        <div className='flex justify-center items-center py-10 text-gray-500'>
          Loading doctor details...
        </div>
      ) : (
        <div className='space-y-4'>
          <div className='flex gap-6 pt-4'>
            <div className='w-40 flex flex-col items-center gap-2'>
              <div className='w-40 h-40 rounded-lg bg-gray-200 flex items-center justify-center overflow-hidden'>
                {profilePictureUrl ? (
                  <img
                    src={profilePictureUrl}
                    alt={`${doctorName} profile`}
                    className='w-full h-full object-cover'
                    onError={(e) => {
                      const target = e.currentTarget;
                      target.style.display = 'none';
                      const parent = target.parentElement;
                      if (parent) {
                        parent.innerHTML = `
                          <div class="flex flex-col items-center justify-center text-sm text-gray-500">
                            <span>Photo</span>
                            <span>Unavailable</span>
                          </div>
                        `;
                      }
                    }}
                  />
                ) : (
                  <div className='flex flex-col items-center text-sm text-gray-500'>
                    <span>Photo</span>
                    <span>Unavailable</span>
                  </div>
                )}
              </div>
              {isSuperAdmin ? (
                <button
                  type='button'
                  className='text-xs text-blue-600 hover:underline cursor-pointer'
                  onClick={() => {
                    const email =
                      doctorProfile?.general?.workEmail || invoiceDoctor?.email;
                    if (!email) return;
                    if (doctorProfile) {
                      try {
                        sessionStorage.setItem(
                          'cachedDoctorProfile',
                          JSON.stringify({
                            email,
                            profile: doctorProfile,
                            cachedAt: Date.now(),
                          })
                        );
                      } catch {
                        // ignore storage failures
                      }
                    }
                    navigate(
                      `${PATHS.DOCTOR_PROFILE}?email=${encodeURIComponent(email)}`
                    );
                  }}
                >
                  Go to Profile
                </button>
              ) : null}
            </div>

            <div className='flex-1 flex flex-col gap-3 text-sm'>
              <div className='flex gap-2'>
                <span className='font-semibold text-gray-900 w-40'>Name :</span>
                <span className='font-semibold text-gray-900'>
                  {capitalizeFirstLetter(doctorName)}
                </span>
              </div>

              <div className='flex gap-2'>
                <span className='font-semibold text-gray-900 w-40'>Age :</span>
                <span className='font-semibold text-gray-900'>{doctorAge}</span>
              </div>

              <div className='flex gap-2'>
                <span className='font-semibold text-gray-900 w-40'>DOB :</span>
                <span className='font-semibold text-gray-900'>
                  {doctorDob !== '-' ? formatDate(doctorDob) : '-'}
                </span>
              </div>

              <div className='flex gap-2'>
                <span className='font-semibold text-gray-900 w-40'>
                  Doctor ID :
                </span>
                <span className='font-semibold text-gray-900'>{doctorId}</span>
              </div>

              <div className='flex gap-2'>
                <span className='font-semibold text-gray-900 w-40'>
                  Department :
                </span>
                <span className='font-semibold text-gray-900'>
                  {department}
                </span>
              </div>

              <div className='flex gap-2'>
                <span className='font-semibold text-gray-900 w-40'>
                  Educational <br />
                  Qualifications :
                </span>
                <span className='font-semibold text-gray-900'>
                  {qualifications}
                </span>
              </div>
              <div className='flex gap-2'>
                <span className='font-semibold text-gray-900 w-40'>
                  Date of Joining :
                </span>
                <span className='font-semibold text-gray-900'>
                  {dateOfJoining}
                </span>
              </div>
              <div className='flex gap-2'>
                <span className='font-semibold text-gray-900 w-40'>
                  Experience :
                </span>
                <span className='font-semibold text-gray-900'>
                  {computeExperience}
                </span>
              </div>
            </div>
          </div>
          <div className='border-t border-gray-200 px-6 py-4'>
            <div className='text-center text-sm text-gray-500'>
              <div className='powered-by flex items-center justify-center gap-2'>
                <span>Powered By</span>
                <img
                  src='/images/Vector.png'
                  alt='Arca Logo'
                  className='arca-logo'
                  onError={(e) => {
                    const target = e.currentTarget;
                    target.style.display = 'none';
                    const textSpan = target.nextElementSibling as HTMLElement;
                    if (textSpan) {
                      textSpan.classList.remove('hidden');
                    }
                  }}
                />
                <span className='arca-logo-text hidden'>ARCA</span>
              </div>
            </div>
          </div>
        </div>
      )}
    </Modal>
  );
};

export default DoctorProfileModal;
