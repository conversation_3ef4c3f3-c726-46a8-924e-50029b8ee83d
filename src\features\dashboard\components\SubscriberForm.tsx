import { ChevronDown, ChevronUp } from 'lucide-react';
import React, {
  memo,
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState,
} from 'react';

import AppButton from '../../../components/Common/AppButton';
import TextInput from '../../../components/Common/MUIInput';
import SelectInput from '../../../components/Common/MUISelect';
import DatePicker from '../../../components/DatePicker';
import { useToast } from '../../../contexts/ToastContext';
import {
  createSubscriber,
  CreateSubscriberRequest,
  SubscriberBillingType,
  SubscriberDetail,
  SubscriberStatus,
  updateSubscriber,
} from '../../../store/features/subscription/subscriber.service';
import { SubscriptionPlan } from '../../../store/features/subscription/subscription.service';

interface SubscriberFormProps {
  onCancel: () => void;
  onSuccess?: () => void;
  isEditMode?: boolean;
  subscriberId?: string;
  initialData?: SubscriberDetail | null;
  isLoading?: boolean;
  plans: SubscriptionPlan[];
  plansLoading?: boolean;
}

type StatusValue = SubscriberStatus;
type BillingType = SubscriberBillingType | '7 Days';

type SelectOption = {
  label: string;
  value: string;
};

type PlanValidity = 'Monthly' | 'Yearly' | 'Both';

interface SubscriberFormState {
  organizationName: string;
  planId: string;
  billingType: BillingType;
  status: StatusValue;
  startDate: Date | null;
  endDate: Date | null;
  address: string;
  pincode: string;
  pan: string;
  gstin: string;
  contactPerson: string;
  contactPhone: string;
  email: string;
  autoRenew: boolean;
  description: string;
  totalAmount: string;
}

type SubscriberFormErrors = Partial<
  Pick<
    SubscriberFormState,
    'organizationName' | 'planId' | 'address' | 'contactPerson' | 'email'
  >
>;

const normalizeToStartOfDay = (date: Date): Date => {
  const normalized = new Date(date);
  normalized.setHours(0, 0, 0, 0);
  return normalized;
};

const getAutoEndDate = (
  billingType: BillingType,
  referenceDate?: Date | null
): Date => {
  const base = normalizeToStartOfDay(referenceDate ?? new Date());
  if (billingType === 'Yearly') {
    base.setFullYear(base.getFullYear() + 1);
  } else if (billingType === '7 Days') {
    base.setDate(base.getDate() + 7);
  } else {
    base.setMonth(base.getMonth() + 1);
  }
  return base;
};

const applyBillingTypeWithEndDate = (
  state: SubscriberFormState,
  billingType: BillingType,
  referenceDate?: Date | null
): SubscriberFormState => ({
  ...state,
  billingType,
  endDate: getAutoEndDate(billingType, referenceDate),
});

const formatPhoneNumber = (value: string): string => {
  const digits = value.replace(/\D/g, '');

  if (value.startsWith('+91')) {
    const afterPrefix = value.substring(3).replace(/\D/g, '');
    // Limit to 10 digits after +91
    const limitedDigits = afterPrefix.substring(0, 10);
    return `+91${limitedDigits}`;
  }

  if (digits.startsWith('91') && digits.length > 2) {
    const afterCountryCode = digits.substring(2).substring(0, 10);
    return `+91${afterCountryCode}`;
  }

  const limitedDigits = digits.substring(0, 10);
  return `+91${limitedDigits}`;
};

const getInitialFormState = (): SubscriberFormState => ({
  organizationName: '',
  planId: '',
  billingType: 'Monthly',
  status: 'active',
  startDate: new Date(),
  endDate: getAutoEndDate('Monthly'),
  address: '',
  pincode: '',
  pan: '',
  gstin: '',
  contactPerson: '',
  contactPhone: '+91',
  email: '',
  autoRenew: false,
  description: '',
  totalAmount: '',
});

const deriveBillingTypeFromValidity = (
  validity?: PlanValidity
): BillingType => {
  if (validity === 'Yearly') {
    return 'Yearly';
  }
  return 'Monthly';
};

const isValidBillingType = (value: unknown): value is BillingType =>
  value === 'Monthly' || value === 'Yearly' || value === '7 Days';

const STATUS_OPTIONS: Array<{ label: string; value: StatusValue }> = [
  { label: 'Active', value: 'active' },
  { label: 'Expired', value: 'expired' },
  { label: 'Cancelled', value: 'cancelled' },
  { label: 'Pending', value: 'pending' },
  { label: 'Free Trial', value: 'free trial' },
];

const SubscriberForm: React.FC<SubscriberFormProps> = memo(
  function SubscriberForm({
    onCancel,
    onSuccess,
    isEditMode = false,
    subscriberId,
    initialData,
    isLoading = false,
    plans,
    plansLoading = false,
  }) {
    const [isDetailsExpanded, setIsDetailsExpanded] = useState(false);
    const { error: showError, success: showSuccess } = useToast();
    const [planOptions, setPlanOptions] = useState<SelectOption[]>([]);
    const [isSubmitting, setIsSubmitting] = useState(false);
    const [planValidityMap, setPlanValidityMap] = useState<
      Record<string, PlanValidity>
    >({});
    const [planFreeTrialMap, setPlanFreeTrialMap] = useState<
      Record<string, boolean>
    >({});
    const [planAmountMap, setPlanAmountMap] = useState<
      Record<
        string,
        { monthly?: number | undefined; yearly?: number | undefined }
      >
    >({});
    const [formData, setFormData] =
      useState<SubscriberFormState>(getInitialFormState);
    const [panError, setPanError] = useState<string | null>(null);
    const [gstError, setGstError] = useState<string | null>(null);
    const [contactPhoneError, setContactPhoneError] = useState<string | null>(
      null
    );
    const [formErrors, setFormErrors] = useState<SubscriberFormErrors>({});

    const errorFields = useMemo(
      () =>
        new Set<keyof SubscriberFormErrors>([
          'organizationName',
          'planId',
          'address',
          'contactPerson',
          'email',
        ]),
      []
    );

    const parseDateValue = (value?: string | null): Date | null => {
      if (!value) return null;
      const date = new Date(value);
      if (Number.isNaN(date.getTime())) {
        return null;
      }
      return date;
    };

    const handleInputChange = <K extends keyof SubscriberFormState>(
      field: K,
      value: SubscriberFormState[K]
    ) => {
      setFormData((prev) => {
        if (field === 'billingType') {
          const billingTypeValue = value as BillingType;
          let nextState = applyBillingTypeWithEndDate(prev, billingTypeValue);
          if (prev.planId) {
            const autoAmount = getAutoAmountForPlan(
              prev.planId,
              billingTypeValue
            );
            if (autoAmount !== undefined) {
              nextState = { ...nextState, totalAmount: autoAmount };
            }
          }
          return nextState;
        }
        if (field === 'totalAmount') {
          return { ...prev, totalAmount: value as string };
        }
        if (field === 'contactPhone') {
          const formatted = formatPhoneNumber(value as string);
          return { ...prev, contactPhone: formatted };
        }
        return { ...prev, [field]: value };
      });
      if (errorFields.has(field as keyof SubscriberFormErrors)) {
        setFormErrors((prev) => {
          if (!prev[field as keyof SubscriberFormErrors]) {
            return prev;
          }
          const next = { ...prev };
          delete next[field as keyof SubscriberFormErrors];
          return next;
        });
      }
      if (field === 'pan') {
        if (!value || typeof value !== 'string') {
          setPanError(null);
        } else {
          const trimmed = value.trim().toUpperCase();
          if (trimmed.length === 0) {
            setPanError(null);
          } else {
            const panPattern = /^[A-Z]{5}[0-9]{4}[A-Z]$/;
            setPanError(panPattern.test(trimmed) ? null : 'Invalid PAN format');
          }
        }
      }

      if (field === 'gstin') {
        if (!value || typeof value !== 'string') {
          setGstError(null);
        } else {
          const trimmed = value.trim().toUpperCase();
          if (trimmed.length === 0) {
            setGstError(null);
          } else {
            const gstPattern =
              /^[0-9]{2}[A-Z]{5}[0-9]{4}[A-Z][1-9A-Z]Z[0-9A-Z]$/;
            setGstError(
              gstPattern.test(trimmed) ? null : 'Invalid GSTIN format'
            );
          }
        }
      }

      if (field === 'contactPhone') {
        if (!value || typeof value !== 'string') {
          setContactPhoneError(null);
        } else {
          const trimmed = value.trim();
          if (trimmed === '+91' || trimmed.length === 0) {
            setContactPhoneError(null);
          } else {
            const digitsAfterPrefix = trimmed
              .replace('+91', '')
              .replace(/\D/g, '');
            if (digitsAfterPrefix.length === 10) {
              setContactPhoneError(null);
            } else {
              setContactPhoneError(
                'Phone number must have exactly 10 digits after +91'
              );
            }
          }
        }
      }
    };

    useEffect(() => {
      if (!isEditMode) {
        setFormData(getInitialFormState());
        setIsDetailsExpanded(false);
        setFormErrors({});
        setPanError(null);
        setGstError(null);
        setContactPhoneError(null);
      }
    }, [isEditMode]);

    useEffect(() => {
      if (!isEditMode || !initialData || isLoading) {
        return;
      }

      // Check if subscriptionType is "clinic" and use paymentAmount if available
      const isClinicSubscription =
        initialData.subscriptionType?.toLowerCase() === 'clinic';
      const amountToUse =
        isClinicSubscription &&
        initialData.paymentAmount !== undefined &&
        initialData.paymentAmount !== null
          ? String(initialData.paymentAmount)
          : typeof initialData.totalAmount === 'number' &&
              !Number.isNaN(initialData.totalAmount)
            ? String(initialData.totalAmount)
            : '';

      const planId = initialData.planId ?? '';
      const isFreeTrialPlan =
        (plans || []).find((p) => p.id === planId)?.isFreeTrial ?? false;

      const billingType = isFreeTrialPlan
        ? '7 Days'
        : isValidBillingType(initialData.billingType)
          ? initialData.billingType
          : 'Monthly';

      const startDate =
        parseDateValue(initialData.startDate ?? null) ?? new Date();

      setFormData({
        organizationName: initialData.organizationName ?? '',
        planId,
        billingType,
        status: initialData.status ?? 'active',
        startDate,
        endDate:
          parseDateValue(initialData.endDate ?? initialData.validity ?? null) ??
          getAutoEndDate(billingType, startDate),
        address: initialData.address ?? '',
        pincode: initialData.pincode ?? '',
        pan: initialData.pan ?? '',
        gstin: initialData.gstin ?? '',
        contactPerson: initialData.contactPerson ?? '',
        contactPhone: initialData.contactPhone
          ? formatPhoneNumber(initialData.contactPhone)
          : '+91',
        email: initialData.email ?? '',
        autoRenew: initialData.autoRenew ?? false,
        description: initialData.description ?? '',
        totalAmount: amountToUse,
      });
      setIsDetailsExpanded(true);
      setFormErrors({});
    }, [initialData, isEditMode, isLoading, plans]);

    useEffect(() => {
      const fetchedPlans = Array.isArray(plans) ? plans : [];
      const options: SelectOption[] = [];
      const validityMap: Record<string, PlanValidity> = {};
      const freeTrialMap: Record<string, boolean> = {};
      const amountMap: Record<
        string,
        { monthly?: number | undefined; yearly?: number | undefined }
      > = {};

      fetchedPlans.forEach((plan) => {
        if (!plan.id || !plan.planName) return;

        options.push({
          label: plan.planName,
          value: plan.id,
        });

        if (plan.validity === 'Monthly') {
          validityMap[plan.id] = 'Monthly';
        } else if (plan.validity === 'Yearly') {
          validityMap[plan.id] = 'Yearly';
        } else {
          validityMap[plan.id] = 'Both';
        }

        // Track free trial plans
        freeTrialMap[plan.id] = plan.isFreeTrial ?? false;

        amountMap[plan.id] = {
          monthly: plan.monthlyTotal ?? undefined,
          yearly: plan.yearlyTotal ?? undefined,
        };
      });

      setPlanOptions(options);
      setPlanValidityMap(validityMap);
      setPlanFreeTrialMap(freeTrialMap);
      setPlanAmountMap(amountMap);

      setFormData((prev) => {
        const currentValidity = validityMap[prev.planId];
        const isFreeTrial = freeTrialMap[prev.planId];

        if (!prev.planId || !currentValidity) {
          if (options.length === 0) {
            const resetState = { ...prev, planId: '' };
            return applyBillingTypeWithEndDate(resetState, 'Monthly');
          }
          if (isValidBillingType(prev.billingType)) {
            return prev;
          }
          return applyBillingTypeWithEndDate(prev, 'Monthly');
        }

        // If plan is free trial, set billing type to '7 Days'
        if (isFreeTrial) {
          if (prev.billingType === '7 Days') {
            return prev;
          }
          return applyBillingTypeWithEndDate(prev, '7 Days', prev.startDate);
        }

        if (currentValidity === 'Both') {
          if (isValidBillingType(prev.billingType)) {
            return prev;
          }
          return applyBillingTypeWithEndDate(prev, 'Monthly');
        }
        const nextBillingType = deriveBillingTypeFromValidity(currentValidity);

        if (nextBillingType === prev.billingType) {
          return prev;
        }

        return applyBillingTypeWithEndDate(prev, nextBillingType);
      });
    }, [plans]);

    const handleSubmit = async (e: React.FormEvent) => {
      e.preventDefault();
      if (isSubmitting || isLoading) return;

      const trimmedOrganizationName = formData.organizationName.trim();
      const trimmedAddress = formData.address.trim();
      const trimmedContactPerson = formData.contactPerson.trim();
      const trimmedEmail = formData.email.trim();
      const trimmedPlanId = formData.planId.trim();

      const errors: SubscriberFormErrors = {};

      if (!trimmedOrganizationName) {
        errors.organizationName = 'Required';
      }

      if (!trimmedPlanId) {
        errors.planId = 'Please select a subscription plan';
      }

      if (!trimmedAddress) {
        errors.address = 'Required';
      }

      if (!trimmedContactPerson) {
        errors.contactPerson = 'Required';
      }

      if (!trimmedEmail) {
        errors.email = 'Required';
      }

      if (Object.keys(errors).length > 0) {
        setFormErrors(errors);
        if (errors.address || errors.contactPerson || errors.email) {
          setIsDetailsExpanded(true);
        }
        return;
      }

      if (panError) {
        setIsDetailsExpanded(true);
        return;
      }

      if (gstError) {
        setIsDetailsExpanded(true);
        return;
      }

      if (contactPhoneError) {
        setIsDetailsExpanded(true);
        return;
      }

      if (isEditMode && !subscriberId) {
        showError('Error', 'Subscriber information is missing');
        return;
      }

      const endDateISO =
        formData.endDate instanceof Date &&
        !Number.isNaN(formData.endDate.valueOf())
          ? new Date(
              Date.UTC(
                formData.endDate.getFullYear(),
                formData.endDate.getMonth(),
                formData.endDate.getDate()
              )
            )
              .toISOString()
              .split('T')[0]
          : undefined;
      const trimmedDescription = formData.description.trim();
      const amountValue =
        formData.totalAmount && formData.totalAmount.trim() !== ''
          ? Number(formData.totalAmount)
          : undefined;
      const normalizedAmount =
        amountValue !== undefined && !Number.isNaN(amountValue)
          ? amountValue
          : undefined;

      const formattedPhone = formData.contactPhone.trim();
      const finalPhone = formattedPhone.startsWith('+91')
        ? formattedPhone
        : `+91${formattedPhone.replace(/\D/g, '').substring(0, 10)}`;

      const shouldIncludePhone = finalPhone !== '+91' && finalPhone.length > 3;

      const payload: CreateSubscriberRequest = {
        organizationName: trimmedOrganizationName,
        email: trimmedEmail,
        contactPerson: trimmedContactPerson,
        ...(shouldIncludePhone && { contactPhone: finalPhone }),
        address: formData.address.trim(),
        pincode: formData.pincode.trim(),
        pan: formData.pan.trim(),
        gstin: formData.gstin.trim(),
        planId: formData.planId,
        billingType: formData.billingType,
        status: formData.status,
        endDate: endDateISO,
        validity: endDateISO,
        autoRenew: formData.autoRenew,
        description: trimmedDescription,
        totalAmount: normalizedAmount ?? null,
      };

      setIsSubmitting(true);
      setFormErrors({});
      try {
        if (isEditMode && subscriberId) {
          const response = await updateSubscriber(subscriberId, payload);
          showSuccess(
            'Success',
            response?.message || 'Subscriber updated successfully'
          );
          if (onSuccess) {
            onSuccess();
          } else {
            onCancel();
          }
        } else {
          const response = await createSubscriber(payload);
          showSuccess(
            'Success',
            response?.message || 'Subscriber created successfully'
          );
          setFormData(getInitialFormState());
          setFormErrors({});
          setPanError(null);
          setGstError(null);
          setContactPhoneError(null);
          setIsDetailsExpanded(false);
          if (onSuccess) {
            onSuccess();
          } else {
            onCancel();
          }
        }
      } catch (error) {
        const axiosError = error as {
          response?: {
            data?:
              | string
              | {
                  message?: string;
                  error?: string;
                  errors?: string | string[] | { message?: string };
                };
            status?: number;
            statusText?: string;
          };
          message?: string;
        };

        let errorMessage: string | undefined;

        if (axiosError.response?.data) {
          if (typeof axiosError.response.data === 'string') {
            errorMessage = axiosError.response.data;
          } else if (
            typeof axiosError.response.data === 'object' &&
            axiosError.response.data !== null &&
            'message' in axiosError.response.data
          ) {
            errorMessage = (axiosError.response.data as { message?: string })
              .message;
          } else if (
            typeof axiosError.response.data === 'object' &&
            axiosError.response.data !== null &&
            'error' in axiosError.response.data
          ) {
            const errorData = (axiosError.response.data as { error?: unknown })
              .error;
            errorMessage =
              typeof errorData === 'string' ? errorData : undefined;
          } else if (
            typeof axiosError.response.data === 'object' &&
            axiosError.response.data !== null &&
            'errors' in axiosError.response.data
          ) {
            const errors = (
              axiosError.response.data as {
                errors?: unknown;
              }
            ).errors;
            if (typeof errors === 'string') {
              errorMessage = errors;
            } else if (Array.isArray(errors) && errors.length > 0) {
              errorMessage =
                typeof errors[0] === 'string' ? errors[0] : String(errors[0]);
            } else if (
              typeof errors === 'object' &&
              errors !== null &&
              'message' in errors
            ) {
              errorMessage = (errors as { message?: string }).message;
            }
          }
        }

        if (!errorMessage) {
          errorMessage = isEditMode
            ? 'Failed to update subscriber'
            : 'Failed to create subscriber';
        }

        showError('Error', errorMessage);
      } finally {
        setIsSubmitting(false);
      }
    };

    const fieldRefs = {
      organizationName: useRef<HTMLInputElement>(null),
      address: useRef<HTMLInputElement>(null),
      pincode: useRef<HTMLInputElement>(null),
      pan: useRef<HTMLInputElement>(null),
      gstin: useRef<HTMLInputElement>(null),
      contactPhone: useRef<HTMLInputElement>(null),
      contactPerson: useRef<HTMLInputElement>(null),
      email: useRef<HTMLInputElement>(null),
      description: useRef<HTMLInputElement>(null),
      planId: useRef<HTMLInputElement>(null),
      billingType: useRef<HTMLInputElement>(null),
      totalAmount: useRef<HTMLInputElement>(null),
      status: useRef<HTMLInputElement>(null),
    };

    const [pendingFocus, setPendingFocus] = useState<
      keyof typeof fieldRefs | null
    >(null);

    const focusNext = useCallback(
      (currentField: keyof typeof fieldRefs) => {
        const order: (keyof typeof fieldRefs)[] = [
          'organizationName',
          'address',
          'pincode',
          'pan',
          'gstin',
          'contactPhone',
          'contactPerson',
          'email',
          'description',
          'planId',
          'billingType',
          'totalAmount',
          'status',
        ];

        const currentIndex = order.indexOf(currentField);

        if (currentField === 'organizationName' && !isDetailsExpanded) {
          setIsDetailsExpanded(true);
          setPendingFocus('address');
          return;
        }

        for (let i = currentIndex + 1; i < order.length; i++) {
          const nextField = order[i] as keyof typeof fieldRefs;
          const ref = fieldRefs[nextField];
          if (ref.current && !ref.current.disabled) {
            ref.current.focus();
            return;
          }
        }

        handleSubmit({ preventDefault: () => {} } as any);
      },
      [isDetailsExpanded, handleSubmit]
    );

    useEffect(() => {
      if (isDetailsExpanded && pendingFocus) {
        const ref = fieldRefs[pendingFocus];
        if (ref.current) {
          ref.current.focus();
          setPendingFocus(null);
        }
      }
    }, [isDetailsExpanded, pendingFocus]);

    const toggleDetails = () => {
      if (isSubmitting || isLoading) return;
      setIsDetailsExpanded((prev) => !prev);
    };

    const planDerivedValidity = planValidityMap[formData.planId];
    const isPlanFreeTrial = planFreeTrialMap[formData.planId] ?? false;
    const billingTypeSelectOptions = (() => {
      // If plan is free trial, only show 7 Days option
      if (isPlanFreeTrial) {
        return [{ label: '7 Days', value: '7 Days' }];
      }

      if (planDerivedValidity === 'Both') {
        return [
          { label: 'Monthly', value: 'Monthly' },
          { label: 'Yearly', value: 'Yearly' },
        ];
      }

      if (planDerivedValidity === 'Yearly') {
        return [{ label: 'Yearly', value: 'Yearly' }];
      }

      if (planDerivedValidity === 'Monthly') {
        return [{ label: 'Monthly', value: 'Monthly' }];
      }

      return [
        { label: 'Monthly', value: 'Monthly' },
        { label: 'Yearly', value: 'Yearly' },
      ];
    })();

    const isBillingTypeEditable =
      planDerivedValidity === 'Both' && !isPlanFreeTrial;
    const isFormDisabled = isSubmitting || isLoading;
    const planPlaceholder = plansLoading
      ? 'Loading plans...'
      : planOptions.length === 0
        ? 'No plans available'
        : 'Select plan';

    const getAutoAmountForPlan = useCallback(
      (planId?: string | null, billing?: BillingType) => {
        if (!planId) return undefined;
        const entry = planAmountMap[planId];
        if (!entry) return undefined;
        const targetBilling = billing ?? 'Monthly';
        const amount =
          targetBilling === 'Yearly'
            ? (entry.yearly ?? entry.monthly)
            : (entry.monthly ?? entry.yearly);
        return amount !== undefined && !Number.isNaN(amount)
          ? String(amount)
          : undefined;
      },
      [planAmountMap]
    );

    useEffect(() => {
      setFormData((prev) => {
        if (!prev.planId || prev.totalAmount) {
          return prev;
        }
        const autoAmount = getAutoAmountForPlan(prev.planId, prev.billingType);
        if (autoAmount === undefined) {
          return prev;
        }
        return { ...prev, totalAmount: autoAmount };
      });
    }, [getAutoAmountForPlan]);

    if (isEditMode && isLoading && !initialData) {
      return (
        <div className='p-6 text-sm text-gray-500'>
          Loading subscriber details...
        </div>
      );
    }

    return (
      <div className='space-y-6 p-6'>
        <form onSubmit={handleSubmit} className='space-y-4'>
          <div className='w-full'>
            <TextInput
              label='Organization Name *'
              placeholder='Enter the Name'
              value={formData.organizationName}
              disabled={isFormDisabled}
              error={Boolean(formErrors.organizationName)}
              helperText={formErrors.organizationName}
              ref={fieldRefs.organizationName}
              onChange={(e) =>
                handleInputChange('organizationName', e.target.value)
              }
              onKeyDown={(e) => {
                if (e.key === 'Enter') {
                  e.preventDefault();
                  focusNext('organizationName');
                }
              }}
            />
          </div>

          <div className='border border-gray-200 rounded-lg overflow-hidden'>
            <button
              type='button'
              onClick={toggleDetails}
              disabled={isFormDisabled}
              className={`w-full flex items-center justify-between px-4 py-3 bg-gray-50 transition-colors ${
                isFormDisabled
                  ? 'cursor-not-allowed opacity-60'
                  : 'hover:bg-gray-100'
              }`}
            >
              <span className='font-medium text-gray-700'>
                Organization Details
              </span>
              {isDetailsExpanded ? (
                <ChevronUp className='w-5 h-5 text-gray-600' />
              ) : (
                <ChevronDown className='w-5 h-5 text-gray-600' />
              )}
            </button>

            {isDetailsExpanded && (
              <div className='p-4 space-y-4'>
                <div className='w-full'>
                  <TextInput
                    label='Organization Address *'
                    placeholder='Enter full address'
                    value={formData.address}
                    disabled={isFormDisabled}
                    error={Boolean(formErrors.address)}
                    helperText={formErrors.address}
                    ref={fieldRefs.address}
                    onChange={(e) =>
                      handleInputChange('address', e.target.value)
                    }
                    onKeyDown={(e) => {
                      if (e.key === 'Enter') {
                        e.preventDefault();
                        focusNext('address');
                      }
                    }}
                  />
                </div>

                <div className='w-full'>
                  <TextInput
                    label='Pincode'
                    placeholder='Enter'
                    value={formData.pincode}
                    disabled={isFormDisabled}
                    ref={fieldRefs.pincode}
                    onChange={(e) =>
                      handleInputChange('pincode', e.target.value)
                    }
                    onKeyDown={(e) => {
                      if (e.key === 'Enter') {
                        e.preventDefault();
                        focusNext('pincode');
                      }
                    }}
                  />
                </div>

                <div className='w-full'>
                  <TextInput
                    label='PAN'
                    placeholder='**********'
                    value={formData.pan}
                    error={Boolean(panError)}
                    helperText={panError ?? undefined}
                    disabled={isFormDisabled}
                    ref={fieldRefs.pan}
                    onChange={(e) => handleInputChange('pan', e.target.value)}
                    onKeyDown={(e) => {
                      if (e.key === 'Enter') {
                        e.preventDefault();
                        focusNext('pan');
                      }
                    }}
                  />
                </div>

                <div className='w-full'>
                  <TextInput
                    label='GSTIN'
                    placeholder='27**********1Z5'
                    value={formData.gstin}
                    error={Boolean(gstError)}
                    helperText={gstError ?? undefined}
                    disabled={isFormDisabled}
                    ref={fieldRefs.gstin}
                    onChange={(e) => handleInputChange('gstin', e.target.value)}
                    onKeyDown={(e) => {
                      if (e.key === 'Enter') {
                        e.preventDefault();
                        focusNext('gstin');
                      }
                    }}
                  />
                </div>

                <div className='w-full'>
                  <TextInput
                    label='Contact Phone'
                    placeholder='+91 1234567890'
                    type='tel'
                    ref={fieldRefs.contactPhone}
                    value={formData.contactPhone}
                    disabled={isFormDisabled}
                    error={Boolean(contactPhoneError)}
                    helperText={contactPhoneError ?? undefined}
                    onChange={(e) => {
                      const inputValue = e.target.value;

                      if (
                        inputValue.length < 3 ||
                        !inputValue.startsWith('+91')
                      ) {
                        if (inputValue.length < 3) {
                          handleInputChange('contactPhone', '+91');
                        } else {
                          const digits = inputValue
                            .replace(/\D/g, '')
                            .substring(0, 10);
                          handleInputChange('contactPhone', `+91${digits}`);
                        }
                      } else {
                        const afterPrefix = inputValue.substring(3);
                        const digits = afterPrefix
                          .replace(/\D/g, '')
                          .substring(0, 10);
                        handleInputChange('contactPhone', `+91${digits}`);
                      }
                    }}
                    onKeyDown={(e) => {
                      if (e.key === 'Enter') {
                        e.preventDefault();
                        focusNext('contactPhone');
                      }
                    }}
                  />
                </div>

                <div className='w-full'>
                  <TextInput
                    label='Contact Person *'
                    placeholder='Enter the admin name'
                    value={formData.contactPerson}
                    disabled={isFormDisabled}
                    error={Boolean(formErrors.contactPerson)}
                    helperText={formErrors.contactPerson}
                    ref={fieldRefs.contactPerson}
                    onChange={(e) =>
                      handleInputChange('contactPerson', e.target.value)
                    }
                    onKeyDown={(e) => {
                      if (e.key === 'Enter') {
                        e.preventDefault();
                        focusNext('contactPerson');
                      }
                    }}
                  />
                </div>
                <div className='w-full'>
                  <TextInput
                    label='Email Id *'
                    placeholder='Enter email'
                    type='email'
                    value={formData.email}
                    disabled={isFormDisabled || isEditMode}
                    error={Boolean(formErrors.email)}
                    helperText={formErrors.email}
                    ref={fieldRefs.email}
                    onChange={(e) => handleInputChange('email', e.target.value)}
                    onKeyDown={(e) => {
                      if (e.key === 'Enter') {
                        e.preventDefault();
                        focusNext('email');
                      }
                    }}
                  />
                </div>

                <div className='w-full'>
                  <TextInput
                    label='Description'
                    placeholder='Enter description'
                    multiline
                    minRows={3}
                    value={formData.description}
                    disabled={isFormDisabled}
                    ref={fieldRefs.description}
                    onChange={(e) =>
                      handleInputChange('description', e.target.value)
                    }
                    onKeyDown={(e) => {
                      if (e.key === 'Enter') {
                        e.preventDefault();
                        focusNext('description');
                      }
                    }}
                  />
                </div>
              </div>
            )}
          </div>

          <div className='w-full'>
            <SelectInput
              label='Plan *'
              placeholder={planPlaceholder}
              value={formData.planId}
              forceLabelShrink
              error={Boolean(formErrors.planId)}
              onChange={(e) => {
                const selectedPlanId = e.target.value;
                setFormErrors((prev) => {
                  if (!prev.planId) {
                    return prev;
                  }
                  const next = { ...prev };
                  delete next.planId;
                  return next;
                });
                setFormData((prev) => {
                  const validityForPlan = selectedPlanId
                    ? planValidityMap[selectedPlanId]
                    : undefined;
                  const isFreeTrial = selectedPlanId
                    ? planFreeTrialMap[selectedPlanId]
                    : false;

                  let nextState: SubscriberFormState;

                  // If plan is free trial, set billing type to '7 Days'
                  if (isFreeTrial) {
                    const baseState = { ...prev, planId: selectedPlanId };
                    nextState = applyBillingTypeWithEndDate(
                      baseState,
                      '7 Days',
                      prev.startDate
                    );
                  } else if (!validityForPlan) {
                    const fallbackBillingType = isValidBillingType(
                      prev.billingType
                    )
                      ? prev.billingType
                      : 'Monthly';
                    const nextBillingType = selectedPlanId
                      ? fallbackBillingType
                      : 'Monthly';
                    const baseState = { ...prev, planId: selectedPlanId };
                    nextState = applyBillingTypeWithEndDate(
                      baseState,
                      nextBillingType
                    );
                  } else if (validityForPlan === 'Both') {
                    if (isValidBillingType(prev.billingType)) {
                      nextState = { ...prev, planId: selectedPlanId };
                    } else {
                      const baseState = { ...prev, planId: selectedPlanId };
                      nextState = applyBillingTypeWithEndDate(
                        baseState,
                        'Monthly'
                      );
                    }
                  } else {
                    const nextBillingType =
                      deriveBillingTypeFromValidity(validityForPlan);

                    const baseState = { ...prev, planId: selectedPlanId };

                    if (nextBillingType === prev.billingType) {
                      nextState = baseState;
                    } else {
                      nextState = applyBillingTypeWithEndDate(
                        baseState,
                        nextBillingType
                      );
                    }
                  }

                  if (!selectedPlanId) {
                    return { ...nextState, totalAmount: '' };
                  }

                  if (isFreeTrial) {
                    return { ...nextState, totalAmount: '0' };
                  }

                  const autoAmount = getAutoAmountForPlan(
                    selectedPlanId,
                    nextState.billingType
                  );

                  return autoAmount !== undefined
                    ? { ...nextState, totalAmount: autoAmount }
                    : { ...nextState, totalAmount: '' };
                });
              }}
              ref={fieldRefs.planId}
              onKeyDown={(e) => {
                if (e.key === 'Enter') {
                  e.preventDefault();
                  focusNext('planId');
                }
              }}
              options={planOptions}
              disabled={
                isFormDisabled || plansLoading || planOptions.length === 0
              }
              helperText={
                formErrors.planId
                  ? formErrors.planId
                  : plansLoading
                    ? 'Fetching plans...'
                    : planOptions.length === 0
                      ? 'No plans available'
                      : undefined
              }
            />
          </div>

          <div className='w-full'>
            <SelectInput
              label='Type of subscription *'
              value={formData.billingType}
              onChange={(e) =>
                handleInputChange('billingType', e.target.value as BillingType)
              }
              options={billingTypeSelectOptions}
              disabled={!isBillingTypeEditable || isFormDisabled}
              ref={fieldRefs.billingType}
              onKeyDown={(e) => {
                if (e.key === 'Enter') {
                  e.preventDefault();
                  focusNext('billingType');
                }
              }}
            />
          </div>
          <div className='w-full'>
            <TextInput
              label='Amount (₹)'
              placeholder='Enter amount'
              type='text'
              inputProps={{ inputMode: 'decimal' }}
              value={formData.totalAmount}
              disabled={
                isFormDisabled ||
                (formData.planId ? planFreeTrialMap[formData.planId] : false) ||
                (isEditMode &&
                  initialData?.subscriptionType?.toLowerCase() === 'clinic')
              }
              onChange={(e) => {
                const { value } = e.target;
                if (value === '' || /^[0-9]*\.?[0-9]*$/.test(value)) {
                  handleInputChange('totalAmount', value);
                }
              }}
              ref={fieldRefs.totalAmount}
              onKeyDown={(e) => {
                if (e.key === 'Enter') {
                  e.preventDefault();
                  focusNext('totalAmount');
                }
              }}
            />
          </div>

          <div className='w-full'>
            <SelectInput
              label='Status'
              value={formData.status}
              onChange={(e) =>
                handleInputChange('status', e.target.value as StatusValue)
              }
              placeholder='Select status'
              options={STATUS_OPTIONS}
              forceLabelShrink
              disabled={isFormDisabled}
              ref={fieldRefs.status}
              onKeyDown={(e) => {
                if (e.key === 'Enter') {
                  e.preventDefault();
                  focusNext('status');
                }
              }}
            />
          </div>
          <div className='w-full'>
            <label className='block text-sm font-medium text-gray-700 mb-2'>
              Validity Start Date
            </label>
            <DatePicker
              value={formData.startDate}
              onChange={(date) => handleInputChange('startDate', date)}
              placeholder='Select date'
              disabled
            />
          </div>
          <div className='w-full'>
            <label className='block text-sm font-medium text-gray-700 mb-2'>
              Validity End Date
            </label>
            <DatePicker
              value={formData.endDate}
              onChange={(date) => handleInputChange('endDate', date)}
              placeholder='Select date'
              disabled
            />
          </div>

          <div className='flex justify-end space-x-4 pt-4 w-full'>
            <AppButton
              type='button'
              kind='secondary'
              onClick={onCancel}
              disabled={isSubmitting}
              sx={{
                '&:hover': {
                  backgroundColor: 'rgba(0, 0, 0, 0.08)',
                  borderColor: 'rgba(0, 0, 0, 0.3)',
                  transform: 'translateY(-1px)',
                  boxShadow: '0 2px 4px rgba(0, 0, 0, 0.1)',
                },
                transition: 'all 0.2s ease-in-out',
              }}
            >
              Cancel
            </AppButton>
            <AppButton
              type='submit'
              kind='primary'
              disabled={isFormDisabled}
              sx={{
                '&:hover': {
                  backgroundColor: 'rgba(25, 118, 210, 0.85)',
                  transform: 'translateY(-1px)',
                  boxShadow: '0 4px 8px rgba(25, 118, 210, 0.3)',
                },
                transition: 'all 0.2s ease-in-out',
              }}
            >
              {isSubmitting
                ? isEditMode
                  ? 'Updating...'
                  : 'Saving...'
                : isEditMode
                  ? 'Update'
                  : 'Save'}
            </AppButton>
          </div>
        </form>
      </div>
    );
  }
);

SubscriberForm.displayName = 'SubscriberForm';

export default SubscriberForm;
