import { ALLOWED_ADMIN_PANEL_ROLES, ROLES } from '../constants/roles';
import { User } from '../types';

/**
 * Maps EMR role names to internal app roles
 */
export const mapEmrRoleToAppRole = (
  role: string
): (typeof ALLOWED_ADMIN_PANEL_ROLES)[number] | string => {
  if (!role) return role;
  const normalized = role.toLowerCase().trim();
  if (normalized.includes('organization')) {
    return ROLES.ORGANIZATION_ADMIN;
  }
  if (
    normalized === 'super admin' ||
    normalized === 'super_admin' ||
    normalized === 'superadmin'
  ) {
    return ROLES.SUPER_ADMIN;
  }
  if (normalized === 'admin' || normalized === 'administrator') {
    return ROLES.ORGANIZATION_ADMIN;
  }
  return role;
};

/**
 * Maps the EMR User response to the internal User type
 */
export const mapEmrUserToUser = (emrUser: any): User => {
  const mappedRoleName = mapEmrRoleToAppRole(emrUser.userRole);

  return {
    id: emrUser.id,
    email: emrUser.email,
    firstName: emrUser.name?.split(' ')[0] || '',
    lastName: emrUser.name?.split(' ').slice(1).join(' ') || '',
    name: emrUser.name,
    phone: emrUser.phoneNumber || '',
    organizationName: emrUser.organizationName,
    roles: [
      {
        id: emrUser.roleId,
        name: mappedRoleName,
        description: emrUser.userRole,
        permissions:
          emrUser.permissionKeys?.map((key: string) => ({
            id: key,
            key,
            api: `/${key.replace(/\./g, '/')}`,
            methods: ['GET', 'POST', 'PUT', 'DELETE'],
            module: (key.split('.')[0] as 'EMR' | 'MRD' | 'ADMIN') || 'ADMIN',
            actions: ['read'],
          })) || [],
        organizationId: emrUser.organizationId,
        isSystem: true,
        departmentId: '',
        isDefault: false,
        createdAt: new Date(),
      },
    ],
    userRole: emrUser.userRole,
    organizationId: emrUser.organizationId,
    status: emrUser.isActive ? 'active' : 'inactive',
    mustResetPassword: false,
    createdAt: new Date(emrUser.createdAt || new Date()),
    lastLogin: new Date(emrUser.lastLogin || new Date()),
    subscriptionExpiryDate: emrUser.subscriptionExpiryDate
      ? new Date(emrUser.subscriptionExpiryDate)
      : null,
    subscriberId: emrUser.subscriberId || '',
  };
};
