import { useEffect, useRef } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';

import { useAuth } from './useAuth';

export const useSubscriptionCheck = () => {
  const { user, isOrganizationAdmin, isAuthenticated, emrUserInfo } = useAuth();
  const navigate = useNavigate();
  const location = useLocation();
  const lastCheckedPathRef = useRef<string | null>(null);

  useEffect(() => {
    // Only check if user is organization admin and authenticated
    if (!isAuthenticated || !isOrganizationAdmin || !user?.email) {
      return;
    }

    // Only check once per page (when location changes)
    if (lastCheckedPathRef.current === location.pathname) {
      return;
    }

    const checkSubscription = async () => {
      try {
        lastCheckedPathRef.current = location.pathname;

        // Use emrUserInfo from Redux instead of fetching to avoid duplicate API calls
        // that would interfere with CognitoAuthHandler's refreshEmrUserInfo
        const fetchedUser = emrUserInfo || user;

        // Check if user has subscriberId and subscriptionExpiry
        if (fetchedUser?.subscriberId && fetchedUser?.subscriptionExpiry) {
          const expiryDate = new Date(fetchedUser.subscriptionExpiry);
          const now = new Date();

          if (expiryDate < now) {
            // Subscription expired, navigate to unauthorized page
            navigate('/unauthorized?reason=subscription_expired', {
              replace: true,
            });
          }
        }
      } catch (error) {
        console.error('Error checking subscription:', error);
      }
    };

    checkSubscription();
  }, [
    user?.email,
    isOrganizationAdmin,
    isAuthenticated,
    emrUserInfo,
    navigate,
    location.pathname,
  ]);
};
