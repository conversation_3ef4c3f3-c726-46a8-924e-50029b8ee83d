import React, { useCallback, useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';

import { AppDispatch, RootState } from '../../../store';
import {
  clearError,
  clearMessages,
  clearSelection,
  clearSuccessMessage,
  createLabTest,
  exportLabTests,
  fetchLabTests,
  removeLabTests,
  selectAllTests,
  selectTest,
  setDepartmentFilter,
  setIsActiveFilter,
  setLimit,
  setPage,
  setSearchText,
  updateLabTests,
  updateTestInList,
} from '../../../store/features/lab-tests/labTest.slice';
import { getDepartmentFilterValue } from '../services/department.service';
import {
  CreateLabTestRequest,
  LabTestListItem,
  LabTestListParams,
  UpdateLabTestRequest,
} from '../types/labTest.types';

const LAB_TEST_TOKENS_KEY = 'lab_test_continuation_tokens';

const getStoredTokenForPage = (page: number): string | undefined => {
  try {
    const stored = sessionStorage.getItem(LAB_TEST_TOKENS_KEY);
    if (!stored) return page === 1 ? '' : undefined;
    const tokens = JSON.parse(stored);
    return tokens[page] !== undefined
      ? tokens[page]
      : page === 1
        ? ''
        : undefined;
  } catch (e) {
    return page === 1 ? '' : undefined;
  }
};

export const useLabTests = (organizationId?: string) => {
  const dispatch = useDispatch<AppDispatch>();
  const {
    tests,
    loading,
    creating,
    updating,
    error,
    successMessage,
    total,
    page,
    limit,
    searchText,
    departmentFilter,
    isActiveFilter,
    selectedTests,
    isAllSelected,
    continuationToken,
    hasMoreResults,
    totalFetched,
    totalPages,
    bulkUpdate,
  } = useSelector((state: RootState) => state.labTests);

  // Fetch lab tests with current filters
  const fetchLabTestsList = useCallback(
    (customParams?: Partial<LabTestListParams>) => {
      if (!organizationId) return;
      const params: LabTestListParams = {
        organizationId,
        page,
        pageSize: limit,
        ...(searchText && { searchText }),
        ...(departmentFilter && {
          department: getDepartmentFilterValue(departmentFilter),
        }),
        ...(isActiveFilter !== 'all' && {
          isActive: isActiveFilter === 'active',
        }),
        continuationToken: getStoredTokenForPage(customParams?.page || page),
        ...customParams,
      };
      dispatch(fetchLabTests(params));
    },
    [
      dispatch,
      organizationId,
      page,
      limit,
      searchText,
      departmentFilter,
      isActiveFilter,
    ]
  );

  // Update lab tests
  const updateLabTestsList = useCallback(
    async (data: UpdateLabTestRequest) => {
      const result = await dispatch(updateLabTests(data));
      // Only refresh the list for non-bulk updates
      // Bulk updates will be handled by the BulkUpdateStatusIndicator component
      if (updateLabTests.fulfilled.match(result)) {
        const payload = result.payload as { async?: boolean };
        if (!payload?.async) {
          fetchLabTestsList();
        }
      }
      return result;
    },
    [dispatch, fetchLabTestsList]
  );

  // Create lab test
  const createLabTestHandler = useCallback(
    async (data: {
      testName: string;
      displayName?: string;
      department: string;
      organizationCost: number;
      isActive: boolean;
    }) => {
      if (!organizationId)
        return { success: false, message: 'No organization selected' };

      const createData: CreateLabTestRequest = {
        testName: data.testName,
        ...(data.displayName && { displayName: data.displayName }),
        department: data.department,
        organizationCost: data.organizationCost,
        cost: 0.0, // Default cost as per the API structure
        isActive: data.isActive,
        organizationId,
      };

      const result = await dispatch(createLabTest(createData));
      if (createLabTest.fulfilled.match(result)) {
        // Refresh the list after successful creation
        fetchLabTestsList();
      }
      return result;
    },
    [dispatch, organizationId, fetchLabTestsList]
  );

  // Export lab tests
  const exportLabTestsList = useCallback(() => {
    if (!organizationId) return;

    const params: LabTestListParams = {
      organizationId,
      ...(searchText && { searchText }),
      ...(departmentFilter && {
        department: getDepartmentFilterValue(departmentFilter),
      }),
      ...(isActiveFilter !== 'all' && {
        isActive: isActiveFilter === 'active',
      }),
    };

    dispatch(exportLabTests(params));
  }, [dispatch, organizationId, searchText, departmentFilter, isActiveFilter]);

  // Search functionality
  const search = useCallback(
    (searchTerm: string) => {
      dispatch(setSearchText(searchTerm));
    },
    [dispatch]
  );

  // Filter by department
  const filterByDepartment = useCallback(
    (department: string) => {
      dispatch(setDepartmentFilter(department));
    },
    [dispatch]
  );

  // Filter by status
  const filterByStatus = useCallback(
    (status: string) => {
      dispatch(setIsActiveFilter(status));
    },
    [dispatch]
  );

  // Pagination
  const changePage = useCallback(
    (newPage: number) => {
      dispatch(setPage(newPage));
    },
    [dispatch]
  );

  const changeLimit = useCallback(
    (newLimit: number) => {
      dispatch(setLimit(newLimit));
    },
    [dispatch]
  );

  // Selection management
  const toggleTestSelection = useCallback(
    (testId: string, selected?: boolean) => {
      if (selected === undefined) {
        // Toggle if no selected parameter provided
        dispatch(selectTest(testId));
      } else {
        // Set specific selection state
        dispatch(selectTest({ testId, selected }));
      }
    },
    [dispatch]
  );

  const toggleSelectAll = useCallback(() => {
    dispatch(selectAllTests());
  }, [dispatch]);

  const clearTestSelection = useCallback(() => {
    dispatch(clearSelection());
  }, [dispatch]);

  // Update individual test in the list (for optimistic updates)
  const updateTestInListLocal = useCallback(
    (testId: string, updates: Partial<LabTestListItem>) => {
      dispatch(updateTestInList({ testId, updates }));
    },
    [dispatch]
  );

  // Message management
  const clearErrorMessage = useCallback(() => {
    dispatch(clearError());
  }, [dispatch]);

  const clearSuccessMsg = useCallback(() => {
    dispatch(clearSuccessMessage());
  }, [dispatch]);

  const clearAllMessages = useCallback(() => {
    dispatch(clearMessages());
  }, [dispatch]);

  // Clear selection when component unmounts
  useEffect(() => {
    return () => {
      // Only clear selection when unmounting, not when page changes
      clearTestSelection();
    };
  }, [clearTestSelection]);

  // Track organization changes and prevent duplicate calls
  const prevOrgIdRef = React.useRef<string | undefined>();
  const prevParamsRef = React.useRef<string>('');

  // Remove the problematic useEffect that was causing infinite loading
  // The loading state will be managed by the Redux actions and fetchLabTests.pending
  // No need to manually set loading to true when tests.length === 0

  // Auto-fetch when dependencies change
  useEffect(() => {
    if (!organizationId) return;

    // Check if organization changed (not initial render)
    const isOrganizationChange =
      prevOrgIdRef.current && prevOrgIdRef.current !== organizationId;

    if (isOrganizationChange) {
      // Clear data and selections when organization changes
      dispatch({ type: 'labTests/handleOrganizationChange' });
    }

    // Update the ref
    prevOrgIdRef.current = organizationId;

    // Create params object
    const params: LabTestListParams = {
      organizationId,
      page,
      pageSize: limit,
      ...(searchText && { searchText }),
      ...(departmentFilter && {
        department: getDepartmentFilterValue(departmentFilter),
      }),
      ...(isActiveFilter !== 'all' && {
        isActive: isActiveFilter === 'active',
      }),
      continuationToken: getStoredTokenForPage(page),
    };

    // Create a string representation of params to check for changes
    const paramsString = JSON.stringify(params);

    // Only make API call if params have actually changed
    if (prevParamsRef.current !== paramsString) {
      prevParamsRef.current = paramsString;
      dispatch(fetchLabTests(params));
    }
  }, [
    organizationId,
    page,
    limit,
    searchText,
    departmentFilter,
    isActiveFilter,
    dispatch,

    // Note: 'loading' is intentionally excluded to prevent circular dependency
  ]);

  return {
    // Data
    tests,
    loading,
    creating,
    updating,
    error,
    successMessage,
    total,
    page,
    limit,
    searchText,
    departmentFilter,
    isActiveFilter,
    selectedTests,
    isAllSelected,
    continuationToken,
    hasMoreResults,
    totalFetched,
    totalPages,
    bulkUpdate,

    // Actions
    fetchLabTests: fetchLabTestsList,
    createLabTest: createLabTestHandler,
    updateLabTests: updateLabTestsList,
    exportLabTests: exportLabTestsList,
    search,
    filterByDepartment,
    filterByStatus,
    changePage,
    changeLimit,
    selectTest: toggleTestSelection,
    selectAllTests: toggleSelectAll,
    clearSelection: clearTestSelection,
    updateTestInList: updateTestInListLocal,
    removeLabTests: async () => {
      if (!organizationId)
        return { success: false, message: 'No organization selected' };

      const request = isAllSelected
        ? {
            organizationId,
            selectAll: true,
            department: getDepartmentFilterValue(departmentFilter),
          }
        : { organizationId, tests: selectedTests };

      const result = await dispatch(removeLabTests(request));

      if (removeLabTests.fulfilled.match(result)) {
        // Refresh the list after successful removal
        fetchLabTestsList();
      }

      return result.payload;
    },
    clearError: clearErrorMessage,
    clearSuccessMessage: clearSuccessMsg,
    clearMessages: clearAllMessages,

    // Computed values
    hasSelection:
      selectedTests.length > 0 ||
      isAllSelected ||
      (bulkUpdate.isPolling &&
        (bulkUpdate.status?.status === 'PROCESSING' ||
          bulkUpdate.status?.status === 'PENDING')),
    selectedCount: isAllSelected ? total : selectedTests.length,
  };
};
