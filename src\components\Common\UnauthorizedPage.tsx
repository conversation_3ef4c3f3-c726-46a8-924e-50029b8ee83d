import React from 'react';
import { useNavigate, useSearchParams } from 'react-router-dom';

import SignoutIcon from '../../assets/svg/SignoutIcon';
import WarningIcon from '../../assets/svg/WarningIcon';
import YellowLockIcon from '../../assets/svg/YellowLockIcon';
import { useAuth } from '../../hooks/useAuth';
import LoadingSpinner from './LoadingSpinner';

const UnauthorizedPage: React.FC = () => {
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const { user, loading } = useAuth();

  const reason = searchParams.get('reason');
  const isSubscriptionExpired = reason === 'subscription_expired';

  // Show loading indicator if still checking permissions/profile
  // Also check if we're in the middle of a callback process
  const isProcessing =
    sessionStorage.getItem('cognito_processing_callback') === 'true';

  if (loading || isProcessing) {
    return (
      <div className='flex items-center justify-center min-h-screen bg-gray-50'>
        <LoadingSpinner size='lg' text='Verifying permissions...' />
      </div>
    );
  }

  const handleLogout = async () => {
    try {
      // Use unified logout to handle both Cognito and B2C
      const { logout } = await import('../../utils/unifiedAuth');
      await logout();
      // No need to navigate - logout will redirect automatically
    } catch (error) {
      console.error('Logout failed:', error);
      // Fallback: clear everything and redirect manually
      localStorage.clear();
      sessionStorage.clear();
      navigate('/login');
    }
  };

  const handleUpgrade = () => {
    // Navigate to plan page with user data
    const userData = {
      name: user?.name || '',
      email: user?.email || '',
    };
    // Store in localStorage using the correct key
    localStorage.setItem('subscription_user_data', JSON.stringify(userData));
    // Open subscription page in new tab (external site)
    const baseUrl =
      (typeof window !== 'undefined'
        ? (window as any)._env_?.VITE_EMR_URL
        : '') ||
      import.meta.env.VITE_EMR_URL ||
      '';
    window.open(`${baseUrl}/subscription/signup`, '_blank');
  };

  return (
    <div
      className='fixed inset-0 flex items-center justify-center p-6'
      style={{
        background:
          'linear-gradient(180deg, rgba(255, 255, 255, 0.4) 0%, rgba(194, 233, 254, 0.8) 100%)',
      }}
    >
      <div className='p-8 max-w-sm w-full flex flex-col justify-center align-center text-center bg-white rounded-lg shadow-lg'>
        {isSubscriptionExpired ? (
          <>
            <div className='mx-auto flex h-16 w-16 items-center justify-center rounded-full bg-gradient-to-br mb-4'>
              <WarningIcon />
            </div>
            <h3 className='text-xl font-semibold text-[#001926] mb-2'>
              Your plan expired
            </h3>
            <p className='text-[#001926] mb-4'>
              Your organization plan expired, upgrade to continue
            </p>
            <div className='flex flex-col  gap-2 max-w-md justify-center items-center'>
              <button
                onClick={handleUpgrade}
                className='px-4 py-1 text-md text-center w-full sm:w-auto bg-black rounded-3xl text-white '
              >
                <span>Upgrade now</span>
              </button>
              <span>or</span>

              <button
                onClick={handleLogout}
                className='px-8  text-xl font-medium w-full sm:w-auto bg-white text-[#E4626F] hover:text-red-700'
              >
                <span className='flex  flex-row text-center gap-2 justify-center align-center'>
                  <SignoutIcon className='w-5 h-6' />
                  Sign out
                </span>
              </button>
            </div>
          </>
        ) : (
          <>
            <div className='mx-auto flex h-16 w-16 items-center justify-center rounded-full bg-gradient-to-br mb-3'>
              <YellowLockIcon className='h-13 w-13' />
            </div>
            <h3 className='text-xl font-semibold text-[#001926] mb-2'>
              Access Denied
            </h3>
            <p className='text-[#001926] mb-6'>
              You don't have permission to access this application. Please
              contact your administrator if you believe this is an error.
            </p>
            <button
              onClick={handleLogout}
              className='px-8  text-xl font-medium w-full sm:w-auto bg-white text-[#E4626F] hover:text-red-700'
            >
              <span className='flex  flex-row text-center gap-2 justify-center align-center'>
                <SignoutIcon className='w-5 h-6' />
                Sign out
              </span>
            </button>
          </>
        )}
      </div>
    </div>
  );
};

export default UnauthorizedPage;
