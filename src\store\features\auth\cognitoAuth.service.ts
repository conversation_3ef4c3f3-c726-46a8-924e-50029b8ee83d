/**
 * Cognito Authentication Service
 * This service handles fetching user information after Cognito authentication
 * Similar to auth.service.ts but for Cognito
 */

import { EMR_USER_INFO_ENDPOINT } from '../../../constants/api-endpoints';
import api from '../../../services/api';
import {
  getCognitoAccessToken,
  getCognitoUserInfo,
} from '../../../utils/cognitoAuth';

export interface CognitoUserInfo {
  sub: string; // Cognito user ID
  email: string;
  email_verified?: boolean;
  name?: string;
  given_name?: string;
  family_name?: string;
  picture?: string;
  [key: string]: any;
}

export interface EmrUserInfo {
  id: string;
  email: string;
  userRole: string;
  name: string;
  roleId: string;
  organizationId: string;
  organizationName: string;
  userType: string;
  isActive: boolean;
  redirectUrl: string;
  permissionKeys: string[];
  phoneNumber?: string;
  subscriberId?: string;
  [key: string]: any;
}

/**
 * Fetches user information from EMR service using Cognito token
 * @param email User's email address from Cognito
 * @returns Promise with EMR user information including token
 */
export async function fetchEmrUserInfoWithCognito(
  email: string
): Promise<{ user: EmrUserInfo; token: string }> {
  const token = await getCognitoAccessToken();

  if (!token) {
    console.error('No Cognito access token available');
    throw new Error('No Cognito access token available');
  }

  // Check if we have cached user info in localStorage
  const cachedUserInfo = localStorage.getItem('emrUserInfo');
  if (cachedUserInfo) {
    try {
      const parsedInfo = JSON.parse(cachedUserInfo);
      if (parsedInfo.email === email) {
        console.log('Using cached EMR user info');
        return { user: parsedInfo, token };
      }
    } catch (e) {
      console.warn('Failed to parse cached EMR user info', e);
    }
  }

  try {
    // Make the API call to fetch EMR user info
    const response = await api.get<EmrUserInfo>(EMR_USER_INFO_ENDPOINT, {
      params: { email },
      headers: {
        Authorization: `Bearer ${token}`,
        'Content-Type': 'application/json',
      },
    });

    if (!response.data) {
      console.error('No data in response from EMR service');
      throw new Error('No user data in response from EMR service');
    }

    // Store user info in localStorage for future use
    if (response.data) {
      try {
        localStorage.setItem('emrUserInfo', JSON.stringify(response.data));

        // Also store the role separately for quick access
        if (response.data.userRole) {
          localStorage.setItem('userRole', response.data.userRole);
        }

        console.log('EMR user info fetched and cached successfully');
      } catch (e) {
        console.warn('Failed to store EMR user info in localStorage', e);
      }
    }

    return {
      user: response.data,
      token,
    };
  } catch (error: any) {
    console.error('API Call failed:', {
      error,
      message: error.message,
      response: error.response?.data,
    });
    throw error;
  }
}

/**
 * Get Cognito user information from session storage
 */
export async function getCognitoUser(): Promise<CognitoUserInfo | null> {
  return await getCognitoUserInfo();
}

/**
 * Extract email from Cognito user info
 */
export async function getCognitoUserEmail(): Promise<string | null> {
  const userInfo = await getCognitoUser();
  return userInfo?.email || null;
}

const cognitoAuthService = {
  fetchEmrUserInfoWithCognito,
  getCognitoUser,
  getCognitoUserEmail,
};

export default cognitoAuthService;
