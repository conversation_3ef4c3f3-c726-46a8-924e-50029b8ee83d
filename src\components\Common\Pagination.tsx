import Box from '@mui/material/Box';
import IconButton from '@mui/material/IconButton';
import MuiPagination from '@mui/material/Pagination';
import Typography from '@mui/material/Typography';
import { ChevronLeft, ChevronRight } from 'lucide-react';
import React from 'react';

interface PaginationProps {
  page: number;
  limit: number;
  total: number;
  onPageChange: (
    event: React.ChangeEvent<unknown> | React.MouseEvent<unknown>,
    page: number
  ) => void;
  className?: string;
  variant?: 'standard' | 'simple' | undefined;
  hasMoreResults?: boolean | undefined;
}

const Pagination: React.FC<PaginationProps> = ({
  page,
  limit,
  total,
  onPageChange,
  className = '',
  variant = 'standard',
  hasMoreResults = false,
}) => {
  const totalPages = Math.ceil(total / limit);
  const startItem = total === 0 ? 0 : (page - 1) * limit + 1;
  const endItem = Math.min(page * limit, total);

  if (variant === 'simple') {
    return (
      <Box
        className={className}
        sx={{
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'flex-end',
          px: 3,
          py: 2,
          bgcolor: 'background.paper',
          borderTop: '1px solid',
          borderColor: 'divider',
          gap: 2,
        }}
      >
        <IconButton
          onClick={(e) => onPageChange(e, page - 1)}
          disabled={page <= 1}
          size='small'
          sx={{
            border: '1px solid',
            borderColor: 'divider',
            borderRadius: '4px',
            color: page <= 1 ? 'text.disabled' : 'text.primary',
          }}
        >
          <ChevronLeft className='w-4 h-4' />
        </IconButton>

        <Typography
          variant='body2'
          sx={{
            fontWeight: 600,
            bgcolor: 'primary.main',
            color: 'white',
            width: '32px',
            height: '32px',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            borderRadius: '4px',
            textAlign: 'center',
          }}
        >
          {page}
        </Typography>

        <IconButton
          onClick={(e) => onPageChange(e, page + 1)}
          disabled={!hasMoreResults}
          size='small'
          sx={{
            border: '1px solid',
            borderColor: 'divider',
            borderRadius: '4px',
            color: !hasMoreResults ? 'text.disabled' : 'text.primary',
          }}
        >
          <ChevronRight className='w-4 h-4' />
        </IconButton>
      </Box>
    );
  }

  return (
    <Box
      className={className}
      sx={{
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'space-between',
        px: 3,
        py: 2,
        bgcolor: 'background.paper',
        borderTop: '1px solid',
        borderColor: 'divider',
      }}
    >
      <Typography variant='body2' color='text.secondary'>
        Showing {startItem} to {endItem} of {total} results
      </Typography>
      <MuiPagination
        page={page}
        count={totalPages}
        onChange={onPageChange}
        color='primary'
        shape='rounded'
        siblingCount={1}
        boundaryCount={1}
        size='small'
      />
    </Box>
  );
};

export default Pagination;
