import axios from 'axios';
import React, { useEffect, useMemo, useState } from 'react';
import { useLocation } from 'react-router-dom';

import LoadingSpinner from '../../../components/Common/LoadingSpinner';
import doctorProfileService, {
  Certification,
  DoctorProfileExperience,
  DoctorProfileQualification,
  DoctorProfileResponse,
} from '../../../services/doctorProfile.service';
import { capitalizeFirstLetter } from '../../../utils/inputUtils';

interface CachedDoctorProfile {
  email: string;
  profile: DoctorProfileResponse;
  cachedAt?: number;
}

type TabType =
  | 'general'
  | 'personal'
  | 'emergency'
  | 'bank'
  | 'family'
  | 'insurance'
  | 'documents'
  | 'qualifications'
  | 'certifications'
  | 'employment'
  | 'languages';

const formatDate = (value?: string) => {
  if (!value) return '--';
  const date = new Date(value);
  if (Number.isNaN(date.getTime())) return value;
  return date.toLocaleDateString('en-IN', {
    day: '2-digit',
    month: '2-digit',
    year: 'numeric',
  });
};

const extractFilename = (url?: string): string => {
  if (!url) return 'Document';
  try {
    const urlObj = new URL(url);
    // Extract from path, e.g., /path/to/Vector.png
    const pathParts = urlObj.pathname.split('/');
    const filenameFromPath = pathParts[pathParts.length - 1];
    // Decode URL-encoded filename
    const decoded = filenameFromPath
      ? decodeURIComponent(filenameFromPath)
      : '';
    return decoded || 'Document';
  } catch {
    return 'Document';
  }
};

const areAddressesSame = (
  permanent?: {
    home?: string;
    street?: string;
    city?: string;
    pinCode?: string;
    district?: string;
    state?: string;
    country?: string;
    phone?: string;
    mobile?: string;
    email?: string;
    proof?: { description?: string; url?: string };
  },
  current?: {
    home?: string;
    street?: string;
    city?: string;
    pinCode?: string;
    district?: string;
    state?: string;
    country?: string;
    phone?: string;
    mobile?: string;
    email?: string;
    proof?: { description?: string; url?: string };
  }
): boolean => {
  if (!permanent || !current) return false;

  return (
    permanent.home === current.home &&
    permanent.street === current.street &&
    permanent.city === current.city &&
    permanent.pinCode === current.pinCode &&
    permanent.district === current.district &&
    permanent.state === current.state &&
    permanent.country === current.country &&
    permanent.phone === current.phone &&
    permanent.mobile === current.mobile &&
    permanent.email === current.email &&
    permanent.proof?.description === current.proof?.description &&
    permanent.proof?.url === current.proof?.url
  );
};

const InfoRow: React.FC<{ label: string; value?: React.ReactNode }> = ({
  label,
  value,
}) => (
  <div className='flex gap-3 text-sm py-1.5'>
    <span className='font-medium text-gray-700 '>{label}</span>
    <span className='text-gray-900'>{value ?? '--'}</span>
  </div>
);

const SectionTitle: React.FC<{ children: React.ReactNode; id: string }> = ({
  children,
  id,
}) => (
  <h3
    id={id}
    className='text-sm font-semibold text-gray-800 mb-3 pb-2  scroll-mt-40'
  >
    {children}
  </h3>
);

const Table = <T extends { uuId?: string }>(props: {
  headers: string[];
  rows: T[];
  renderRow: (row: T, idx: number) => React.ReactNode;
  columnWidths?: string[]; // e.g., ['200px', '150px', ...]
}) => {
  const { headers, rows, renderRow, columnWidths } = props;
  if (!rows || rows.length === 0)
    return <div className='text-sm text-gray-500'>No data available</div>;

  return (
    <div className='border rounded-lg overflow-hidden'>
      <table className='w-full table-fixed'>
        <colgroup>
          {headers.map((_, idx) => (
            <col key={idx} style={{ width: columnWidths?.[idx] || 'auto' }} />
          ))}
        </colgroup>
        <thead>
          <tr style={{ backgroundColor: '#02537D' }}>
            {headers.map((h, idx) => (
              <th
                key={h}
                className={`px-4 py-3 text-left text-sm font-semibold text-white ${idx < headers.length - 1 ? 'border-r border-white/20' : ''}`}
              >
                {h}
              </th>
            ))}
          </tr>
        </thead>
        <tbody>
          {rows.map((row, idx) => (
            <tr
              key={row.uuId || idx}
              className={`border-t ${idx % 2 === 0 ? 'bg-white' : 'bg-gray-50'}`}
            >
              {renderRow(row, idx)}
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  );
};

const DoctorProfilePage: React.FC = () => {
  const { search } = useLocation();
  const email = useMemo(
    () => new URLSearchParams(search).get('email') || '',
    [search]
  );

  const cachedProfile = useMemo(() => {
    try {
      const raw = sessionStorage.getItem('cachedDoctorProfile');
      if (!raw) return null;
      const parsed = JSON.parse(raw) as CachedDoctorProfile;
      if (parsed?.email && parsed.profile) return parsed;
      return null;
    } catch {
      return null;
    }
  }, []);

  const [profile, setProfile] = useState<DoctorProfileResponse | null>(
    cachedProfile?.profile ?? null
  );
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState<TabType>('general');

  useEffect(() => {
    const loadProfile = async () => {
      if (!email) {
        setError('Email is required to view doctor profile.');
        return;
      }
      if (cachedProfile?.email === email && cachedProfile.profile) {
        setProfile(cachedProfile.profile);
        setError(null);
        setLoading(false);
        return;
      }
      setLoading(true);
      setError(null);
      try {
        const data =
          await doctorProfileService.fetchDoctorProfileByEmail(email);
        setProfile(data);
        try {
          sessionStorage.setItem(
            'cachedDoctorProfile',
            JSON.stringify({ email, profile: data, cachedAt: Date.now() })
          );
        } catch {
          // ignore storage failures
        }
      } catch (err: unknown) {
        if (axios.isCancel(err)) return;
        let message = 'Unable to load doctor profile.';
        if (axios.isAxiosError(err)) {
          message = err.response?.data?.message || err.message;
        } else if (err instanceof Error) {
          message = err.message;
        }
        setError(message);
      } finally {
        setLoading(false);
      }
    };
    loadProfile();
  }, [email, cachedProfile]);

  // Scroll to section when tab is clicked
  const scrollToSection = (sectionId: string) => {
    const element = document.getElementById(sectionId);
    if (element) {
      element.scrollIntoView({ behavior: 'smooth', block: 'start' });
      setActiveTab(sectionId as TabType);
    }
  };

  if (loading) {
    return (
      <div className='flex items-center justify-center h-full py-12'>
        <LoadingSpinner size='md' text='Loading doctor profile...' />
      </div>
    );
  }

  const tabs: { id: TabType; label: string }[] = [
    { id: 'general', label: 'General Details' },
    { id: 'personal', label: 'Personal Details' },
    { id: 'emergency', label: 'Emergency Details' },
    { id: 'bank', label: 'Bank Details' },
    { id: 'family', label: 'Family' },
    { id: 'insurance', label: 'Medical Insurance' },
    { id: 'documents', label: 'Documents' },
    { id: 'qualifications', label: 'Qualifications' },
    { id: 'certifications', label: 'Certifications' },
    { id: 'employment', label: 'Employment Background' },
    { id: 'languages', label: 'Languages Known' },
  ];

  return (
    <div className='min-h-screen bg-white'>
      <div className='bg-white border-b sticky top-0 z-50'>
        <div className='px-6 py-4 '>
          <div className='flex items-center gap-2 mb-4'>
            <button
              onClick={() => window.history.back()}
              className='text-sm font-extrabold text-black hover:text-gray-700'
            >
              &lt;
            </button>
            <h1 className='text-xl font-semibold text-gray-900'>
              {capitalizeFirstLetter(profile?.general?.fullName) ||
                'Doctor Profile'}
            </h1>
          </div>

          {error ? (
            <div className='text-red-600 text-sm mb-4'>{error}</div>
          ) : null}

          {/* Tabs Navigation */}
          <div className='flex gap-1 flex-wrap'>
            {tabs.map((tab) => (
              <button
                key={tab.id}
                onClick={() => scrollToSection(tab.id)}
                className={`px-3 py-2 text-sm font-medium whitespace-nowrap transition-colors ${
                  activeTab === tab.id
                    ? 'text-gray-900 border-b-2 border-black'
                    : 'text-gray-500 hover:text-gray-900 hover:bg-gray-50'
                }`}
              >
                {tab.label}
              </button>
            ))}
          </div>
        </div>
      </div>

      <div className='px-6 pt-6 bg-white'>
        {!profile ? (
          <div className='text-gray-600 text-sm'>
            {email
              ? 'No profile data available.'
              : 'Provide an email query parameter to view a doctor profile.'}
          </div>
        ) : (
          <div className='bg-white space-y-6'>
            {/* General Details Section */}
            <div className='pb-6 border-b'>
              <SectionTitle id='general'>General Details</SectionTitle>
              <div className='grid grid-cols-1 md:grid-cols-4 gap-x-3'>
                <InfoRow label='Name' value={profile?.general?.fullName} />
                <InfoRow
                  label='Designation'
                  value={profile?.general?.designation}
                />
                <InfoRow
                  label='Department'
                  value={profile?.general?.department}
                />

                <InfoRow
                  label='Contact Number'
                  value={profile?.general?.contactNumber}
                />
                <InfoRow label='Email ID' value={profile?.general?.workEmail} />
                <InfoRow
                  label='Employee ID'
                  value={
                    profile?.general?.doctorId || profile?.general?.doctorID
                  }
                />
                <InfoRow
                  label='Date of Birth'
                  value={formatDate(profile?.personal?.dob)}
                />
              </div>
            </div>

            {/* Personal Details Section */}
            <div className='pb-6 border-b space-y-6'>
              <div>
                <SectionTitle id='personal'>Personal Details</SectionTitle>
                <div className='grid grid-cols-1 md:grid-cols-4 gap-x-8'>
                  <InfoRow
                    label='Marital Status'
                    value={profile?.personal?.maritalStatus}
                  />
                  <InfoRow
                    label='Date of Marriage'
                    value={formatDate(profile?.personal?.dateOfWedding)}
                  />
                  <InfoRow
                    label='Blood Group'
                    value={profile?.personal?.bloodGroup}
                  />
                  <InfoRow
                    label='Height'
                    value={
                      profile?.personal?.height
                        ? `${profile.personal.height} cm`
                        : '--'
                    }
                  />
                  <InfoRow
                    label='Weight'
                    value={
                      profile?.personal?.weight
                        ? `${profile.personal.weight} kg`
                        : '--'
                    }
                  />
                  <InfoRow
                    label='Identification Mark'
                    value={profile?.personal?.identificationMark}
                  />
                  <InfoRow
                    label='Nationality'
                    value={profile?.personal?.nationality}
                  />
                  <InfoRow
                    label='Religion'
                    value={profile?.personal?.religion}
                  />
                  <InfoRow
                    label='ID Proof'
                    value={
                      profile?.personal?.idProof?.url ? (
                        <a
                          href={profile.personal.idProof.url}
                          target='_blank'
                          rel='noopener noreferrer'
                          className='text-blue-600 hover:text-blue-800 underline'
                        >
                          {extractFilename(profile.personal.idProof.url)}
                        </a>
                      ) : (
                        '--'
                      )
                    }
                  />
                  <InfoRow
                    label='Are you disabled?'
                    value={
                      profile?.personal?.isPersonWithDisability ? 'Yes' : 'No'
                    }
                  />
                  <InfoRow
                    label='Percentage of disability'
                    value={
                      profile?.personal?.isPersonWithDisability
                        ? profile?.personal?.percentOfDisability
                        : '--'
                    }
                  />
                  <InfoRow
                    label='Category'
                    value={profile?.personal?.category}
                  />
                </div>
              </div>

              <div>
                <div className='grid grid-cols-1 md:grid-cols-3 gap-x-8'>
                  {/* Row 1: Hometown Details */}
                  <InfoRow
                    label='Hometown'
                    value={profile?.personal?.hometownDetails?.hometown}
                  />
                  <InfoRow
                    label='District'
                    value={profile?.personal?.hometownDetails?.district}
                  />
                  <InfoRow
                    label='State'
                    value={profile?.personal?.hometownDetails?.state}
                  />

                  {/* Row 2: Country (hometown) + Birth Details */}
                  <InfoRow
                    label='Country'
                    value={profile?.personal?.hometownDetails?.country}
                  />
                  <InfoRow
                    label='Place of Birth'
                    value={profile?.personal?.birthDetails?.placeOfBirth}
                  />
                  <InfoRow
                    label='District of Birth'
                    value={profile?.personal?.birthDetails?.district}
                  />

                  {/* Row 3: Birth State and Country */}
                  <InfoRow
                    label='State (Birth)'
                    value={profile?.personal?.birthDetails?.state}
                  />
                  <InfoRow
                    label='Country of Birth'
                    value={profile?.personal?.birthDetails?.country}
                  />
                </div>
              </div>

              <div>
                <h4 className='text-sm font-semibold text-gray-800 mb-3'>
                  Permanent Address
                </h4>
                <div className='grid grid-cols-1 md:grid-cols-4 gap-x-8'>
                  {/* Row 1: House Name, Pincode, Street, City */}
                  <InfoRow
                    label='House Name'
                    value={profile?.personal?.address?.permanent?.home}
                  />
                  <InfoRow
                    label='Pincode'
                    value={profile?.personal?.address?.permanent?.pinCode}
                  />
                  <InfoRow
                    label='Street'
                    value={profile?.personal?.address?.permanent?.street}
                  />
                  <InfoRow
                    label='City'
                    value={profile?.personal?.address?.permanent?.city}
                  />

                  {/* Row 2: District, State, Country, Address Proof */}
                  <InfoRow
                    label='District'
                    value={profile?.personal?.address?.permanent?.district}
                  />
                  <InfoRow
                    label='State'
                    value={profile?.personal?.address?.permanent?.state}
                  />
                  <InfoRow
                    label='Country'
                    value={profile?.personal?.address?.permanent?.country}
                  />
                  <InfoRow
                    label='Address Proof'
                    value={
                      profile?.personal?.address?.permanent?.proof?.url ? (
                        <a
                          href={profile.personal.address.permanent.proof.url}
                          target='_blank'
                          rel='noopener noreferrer'
                          className='text-blue-600 hover:text-blue-800 underline'
                        >
                          {extractFilename(
                            profile.personal.address.permanent.proof.url
                          )}
                        </a>
                      ) : (
                        '--'
                      )
                    }
                  />

                  {/* Row 3: Proof Description */}
                  <InfoRow
                    label='Proof Description'
                    value={
                      profile?.personal?.address?.permanent?.proof?.description
                    }
                  />
                </div>
              </div>

              <div>
                <h4 className='text-sm font-semibold text-gray-800 mb-3'>
                  Present Address
                </h4>
                {areAddressesSame(
                  profile?.personal?.address?.permanent,
                  profile?.personal?.address?.current
                ) ? (
                  <div className='text-sm text-gray-700 py-2'>
                    Same as Permanent
                  </div>
                ) : (
                  <div className='grid grid-cols-1 md:grid-cols-4 gap-x-8'>
                    {/* Row 1: House Name, Pincode, Street, City */}
                    <InfoRow
                      label='House Name'
                      value={profile?.personal?.address?.current?.home}
                    />
                    <InfoRow
                      label='Pincode'
                      value={profile?.personal?.address?.current?.pinCode}
                    />
                    <InfoRow
                      label='Street'
                      value={profile?.personal?.address?.current?.street}
                    />
                    <InfoRow
                      label='City'
                      value={profile?.personal?.address?.current?.city}
                    />

                    <InfoRow
                      label='District'
                      value={profile?.personal?.address?.current?.district}
                    />
                    <InfoRow
                      label='State'
                      value={profile?.personal?.address?.current?.state}
                    />
                    <InfoRow
                      label='Country'
                      value={profile?.personal?.address?.current?.country}
                    />
                    <InfoRow
                      label='Address Proof'
                      value={
                        profile?.personal?.address?.current?.proof?.url ? (
                          <a
                            href={profile.personal.address.current.proof.url}
                            target='_blank'
                            rel='noopener noreferrer'
                            className='text-blue-600 hover:text-blue-800 underline'
                          >
                            {extractFilename(
                              profile.personal.address.current.proof.url
                            )}
                          </a>
                        ) : (
                          '--'
                        )
                      }
                    />

                    {/* Row 3: Proof Description */}
                    <InfoRow
                      label='Proof Description'
                      value={
                        profile?.personal?.address?.current?.proof?.description
                      }
                    />
                  </div>
                )}
              </div>
            </div>

            <div className='pb-0 border-b'>
              <SectionTitle id='emergency'>Emergency Details</SectionTitle>
              {!profile?.emergencyContacts ||
              profile.emergencyContacts.length === 0 ? (
                <div className='text-sm text-gray-500'>No data available</div>
              ) : (
                <div className='space-y-0'>
                  {profile.emergencyContacts.map((contact, index) => (
                    <div
                      key={contact.uuId || index}
                      className=' rounded-lg p-4'
                    >
                      <div className='grid grid-cols-1 md:grid-cols-4 gap-x-8'>
                        {/* Row 1: Name, Relationship, Place, Residence Phone */}
                        <InfoRow
                          label={`Name (${index + 1})`}
                          value={contact.name}
                        />
                        <InfoRow
                          label='Relationship'
                          value={contact.relation}
                        />
                        <InfoRow label='Place' value={contact.city} />
                        <InfoRow
                          label='Residence Phone'
                          value={contact.contactNumber}
                        />

                        {/* Row 2: Mobile, Email */}
                        <InfoRow label='Mobile' value={contact.mobile} />
                        <InfoRow label='Email' value={contact.email} />
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>

            <div className='pb-6 border-b'>
              <SectionTitle id='bank'>Bank Details</SectionTitle>
              <div className='grid grid-cols-1 md:grid-cols-4 gap-x-4'>
                {/* Row 1: Bank Name, Branch, Account Number, IFSC Code */}
                <InfoRow label='Bank Name' value={profile?.bankDetails?.bank} />
                <InfoRow label='Branch' value={profile?.bankDetails?.branch} />
                <InfoRow
                  label='Account Number'
                  value={profile?.bankDetails?.accountNumber}
                />
                <InfoRow label='IFSC Code' value={profile?.bankDetails?.ifsc} />

                <InfoRow
                  label='Upload Document'
                  value={
                    profile?.bankDetails?.document ? (
                      <a
                        href={profile.bankDetails.document}
                        target='_blank'
                        rel='noopener noreferrer'
                        className='text-blue-600 hover:text-blue-800 underline'
                      >
                        {extractFilename(profile.bankDetails.document)}
                      </a>
                    ) : (
                      '--'
                    )
                  }
                />
              </div>
            </div>

            <div className='pb-6 border-b'>
              <SectionTitle id='family'>Family</SectionTitle>
              {!profile?.family || profile.family.length === 0 ? (
                <div className='text-sm text-gray-500'>No data available</div>
              ) : (
                <div className='space-y-4'>
                  {profile.family.map((member, index) => (
                    <div
                      key={member.uuId || index}
                      className='border rounded-lg p-4'
                    >
                      <div className='grid grid-cols-1 md:grid-cols-5 gap-x-4'>
                        <InfoRow label='Name' value={member.name} />
                        <InfoRow label='Relationship' value={member.relation} />
                        <InfoRow label='Dependent' value={member.dependent} />
                        <InfoRow label='DOB' value={formatDate(member.dob)} />
                        <InfoRow
                          label='Status'
                          value={
                            <span
                              className={`inline-block px-2 py-1 rounded text-xs ${
                                member.status === 'verified'
                                  ? 'bg-green-100 text-green-800'
                                  : member.status === 'in_review'
                                    ? 'bg-yellow-100 text-yellow-800'
                                    : 'bg-gray-100 text-gray-800'
                              }`}
                            >
                              {member.status || '--'}
                            </span>
                          }
                        />

                        {/* Row 2: Aadhar Number, Occupation, Documents, Status */}
                        <InfoRow
                          label='Aadhar Number'
                          value={member.aadharNumber}
                        />
                        <InfoRow label='Occupation' value={member.occupation} />
                        <InfoRow label='Documents' value={member.documents} />
                        <InfoRow
                          label='Status'
                          value={
                            <span
                              className={`inline-block px-2 py-1 rounded text-xs ${
                                member.status === 'verified'
                                  ? 'bg-green-100 text-green-800'
                                  : member.status === 'in_review'
                                    ? 'bg-yellow-100 text-yellow-800'
                                    : 'bg-gray-100 text-gray-800'
                              }`}
                            >
                              {member.status || '--'}
                            </span>
                          }
                        />
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>

            <div className='pb-6 border-b'>
              <SectionTitle id='insurance'>Medical Insurance</SectionTitle>
              {!profile?.insurance || profile.insurance.length === 0 ? (
                <div className='text-sm text-gray-500'>No data available</div>
              ) : (
                <div className='space-y-4'>
                  {profile.insurance.map((policy, index) => (
                    <div
                      key={policy.uuId || index}
                      className='border rounded-lg p-4'
                    >
                      <div className='grid grid-cols-1 md:grid-cols-5 gap-x-4'>
                        <InfoRow
                          label='Policy Name'
                          value={policy.policyName}
                        />
                        <InfoRow
                          label='Policy Number'
                          value={policy.policyNumber}
                        />
                        <InfoRow
                          label='From'
                          value={formatDate(policy.validFrom)}
                        />
                        <InfoRow
                          label='To'
                          value={formatDate(policy.validTo)}
                        />
                        <InfoRow
                          label='Status'
                          value={
                            <span
                              className={`inline-block px-2 py-1 rounded text-xs ${
                                policy.status === 'ACTIVE'
                                  ? 'bg-green-100 text-green-800'
                                  : 'bg-gray-100 text-gray-800'
                              }`}
                            >
                              {policy.status || '--'}
                            </span>
                          }
                        />
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>

            <div className='pb-6 border-b space-y-6'>
              <SectionTitle id='documents'>Document Details</SectionTitle>
              <div className='grid grid-cols-1 md:grid-cols-4 gap-x-4'>
                <InfoRow
                  label='Aadhar Number'
                  value={profile?.documents?.aadhar?.number}
                />
                <InfoRow
                  label='Name in Aadhar'
                  value={profile?.documents?.aadhar?.name}
                />
                <InfoRow
                  label='Document Proof'
                  value={
                    profile?.documents?.aadhar?.url ? (
                      <a
                        href={profile.documents.aadhar.url}
                        target='_blank'
                        rel='noopener noreferrer'
                        className='text-blue-600 hover:text-blue-800 underline'
                      >
                        {extractFilename(profile.documents.aadhar.url)}
                      </a>
                    ) : (
                      profile?.documents?.aadhar?.description || '--'
                    )
                  }
                />
                <InfoRow
                  label='Passport'
                  value={profile?.documents?.passport?.number}
                />

                <InfoRow
                  label='Name in Passport'
                  value={profile?.documents?.passport?.name}
                />
                <InfoRow
                  label='Issue Date'
                  value={formatDate(profile?.documents?.aadhar?.issuedAt)}
                />
                <InfoRow
                  label='Renewal Date'
                  value={formatDate(profile?.documents?.passport?.renewedAt)}
                />
                <InfoRow
                  label='Issued Place'
                  value={profile?.documents?.passport?.issuedPlace}
                />

                {/* Row 3: Document Proof (Passport), PAN, Name in PAN, Issue Date (PAN) */}
                <InfoRow
                  label='Document Proof'
                  value={
                    profile?.documents?.passport?.url ? (
                      <a
                        href={profile.documents.passport.url}
                        target='_blank'
                        rel='noopener noreferrer'
                        className='text-blue-600 hover:text-blue-800 underline'
                      >
                        {extractFilename(profile.documents.passport.url)}
                      </a>
                    ) : (
                      profile?.documents?.passport?.description || '--'
                    )
                  }
                />
                <InfoRow
                  label='PAN'
                  value={profile?.documents?.panCard?.number}
                />
                <InfoRow
                  label='Name in PAN'
                  value={profile?.documents?.panCard?.name}
                />
                <InfoRow
                  label='Issue Date'
                  value={formatDate(profile?.documents?.passport?.issuedAt)}
                />

                {/* Row 4: Document Proof (PAN), Issue Date (PAN) */}
                <InfoRow
                  label='Document Proof'
                  value={
                    profile?.documents?.panCard?.url ? (
                      <a
                        href={profile.documents.panCard.url}
                        target='_blank'
                        rel='noopener noreferrer'
                        className='text-blue-600 hover:text-blue-800 underline'
                      >
                        {extractFilename(profile.documents.panCard.url)}
                      </a>
                    ) : (
                      profile?.documents?.panCard?.description || '--'
                    )
                  }
                />
                <InfoRow
                  label='Issue Date'
                  value={formatDate(profile?.documents?.panCard?.issuedAt)}
                />
              </div>
            </div>

            {/* Qualifications Section */}
            <div className='pb-6 border-b'>
              <SectionTitle id='qualifications'>Qualifications</SectionTitle>
              <Table<DoctorProfileQualification>
                headers={[
                  'Qualification/Degree',
                  'Specialization',
                  'University',
                  'Institute',
                  'Year',
                  'Duration',
                  'Marks',
                  'Document',
                  'Status',
                ]}
                columnWidths={[
                  '180px',
                  '140px',
                  '140px',
                  '140px',
                  '100px',
                  '100px',
                  '100px',
                  '120px',
                  '100px',
                ]}
                rows={profile?.professionalDetails?.qualifications || []}
                renderRow={(row) => (
                  <>
                    <td className='px-4 py-3 text-sm text-gray-900 border-r border-gray-200'>
                      {row.degree ?? '--'}
                    </td>
                    <td className='px-4 py-3 text-sm text-gray-900 border-r border-gray-200'>
                      {row.specialization ?? '--'}
                    </td>
                    <td className='px-4 py-3 text-sm text-gray-900 border-r border-gray-200'>
                      {row.university ?? '--'}
                    </td>
                    <td className='px-4 py-3 text-sm text-gray-900 border-r border-gray-200'>
                      {row.institute ?? '--'}
                    </td>
                    <td className='px-4 py-3 text-sm text-gray-900 border-r border-gray-200'>
                      {row.yearOfCompletion ?? '--'}
                    </td>
                    <td className='px-4 py-3 text-sm text-gray-900 border-r border-gray-200'>
                      {row.duration ?? '--'}
                    </td>
                    <td className='px-4 py-3 text-sm text-gray-900 border-r border-gray-200'>
                      {row.marks ?? '--'}
                    </td>
                    <td className='px-4 py-3 text-sm text-gray-900 border-r border-gray-200'>
                      {row.doc1 || row.doc2 ? 'Available' : '--'}
                    </td>
                    <td className='px-4 py-3 text-sm text-gray-900'>
                      {row.status ?? '--'}
                    </td>
                  </>
                )}
              />
            </div>

            {/* Certifications Section */}
            <div className='pb-6 border-b'>
              <SectionTitle id='certifications'>Certifications</SectionTitle>
              <Table<Certification>
                headers={[
                  'Name',
                  'Registration Number',
                  'Valid From',
                  'Valid To',
                  'Date of Updation',
                  'Status',
                ]}
                columnWidths={[
                  '200px',
                  '180px',
                  '140px',
                  '140px',
                  '160px',
                  '120px',
                ]}
                rows={profile?.professionalDetails?.certifications || []}
                renderRow={(row) => (
                  <>
                    <td className='px-4 py-3 text-sm text-gray-900 border-r border-gray-200'>
                      {row.name ?? '--'}
                    </td>
                    <td className='px-4 py-3 text-sm text-gray-900 border-r border-gray-200'>
                      {row.regNumber ?? '--'}
                    </td>
                    <td className='px-4 py-3 text-sm text-gray-900 border-r border-gray-200'>
                      {formatDate(row.validFrom)}
                    </td>
                    <td className='px-4 py-3 text-sm text-gray-900 border-r border-gray-200'>
                      {formatDate(row.validTo)}
                    </td>
                    <td className='px-4 py-3 text-sm text-gray-900 border-r border-gray-200'>
                      {formatDate(row.dateOfUpdation)}
                    </td>
                    <td className='px-4 py-3 text-sm text-gray-900'>
                      <span
                        className={`inline-block px-2 py-1 rounded text-xs ${
                          row.status === 'QUALIFIED'
                            ? 'bg-green-100 text-green-800'
                            : 'bg-gray-100 text-gray-800'
                        }`}
                      >
                        {row.status ?? '--'}
                      </span>
                    </td>
                  </>
                )}
              />
            </div>

            {/* Employment Background Section */}
            <div className='pb-6'>
              <SectionTitle id='employment'>Employment Background</SectionTitle>
              <Table<DoctorProfileExperience>
                headers={[
                  'Hospital Name',
                  'Designation',
                  'From',
                  'To',
                  'Salary',
                  'Documents',
                ]}
                columnWidths={[
                  '250px',
                  '150px',
                  '140px',
                  '140px',
                  '120px',
                  '120px',
                ]}
                rows={profile?.professionalDetails?.experience || []}
                renderRow={(row) => (
                  <>
                    <td className='px-4 py-3 text-sm text-gray-900 border-r border-gray-200'>
                      {row.hospitalName ?? '--'}
                    </td>
                    <td className='px-4 py-3 text-sm text-gray-900 border-r border-gray-200'>
                      {row.designation ?? '--'}
                    </td>
                    <td className='px-4 py-3 text-sm text-gray-900 border-r border-gray-200'>
                      {formatDate(row.duration?.from)}
                    </td>
                    <td className='px-4 py-3 text-sm text-gray-900 border-r border-gray-200'>
                      {formatDate(row.duration?.to)}
                    </td>
                    <td className='px-4 py-3 text-sm text-gray-900 border-r border-gray-200'>
                      {row.salary ?? '--'}
                    </td>
                    <td className='px-4 py-3 text-sm text-gray-900'>
                      {row.doc1 || row.doc2 ? 'Available' : '--'}
                    </td>
                  </>
                )}
              />
            </div>

            <div className='pb-4'>
              <SectionTitle id='languages'>Languages Known</SectionTitle>
              {!profile?.languagesKnown ||
              profile.languagesKnown.length === 0 ? (
                <div className='text-sm text-gray-500'>No data available</div>
              ) : (
                <div className='space-y-2'>
                  {profile.languagesKnown.map((lang, index) => (
                    <div key={index} className='text-sm text-gray-900'>
                      <span className='font-medium'>{lang.language}</span>
                      {lang.fluency && lang.fluency.length > 0 && (
                        <span>
                          {' - '}
                          {lang.fluency
                            .map((f) => f.charAt(0).toUpperCase() + f.slice(1))
                            .join(', ')}
                        </span>
                      )}
                    </div>
                  ))}
                </div>
              )}
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default DoctorProfilePage;
