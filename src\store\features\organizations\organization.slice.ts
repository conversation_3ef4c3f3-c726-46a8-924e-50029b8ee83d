import {
  createAsyncThunk,
  createSlice,
  isPending,
  isRejected,
} from '@reduxjs/toolkit';

import { Organization } from '../../../types';
import { ApiError, InitialState } from '../../../utils/reducer-utils';
import organizationService, {
  OrganizationParams,
} from './organization.service';

type ExtendedInitialState = InitialState<Organization> & {
  organizations: Organization[];
  allOrganizations: Organization[]; // For dropdown/select purposes
  currentOrganization: Organization | null;
  total: number;
  totalPages: number; // Add total pages from API
  page: number;
  limit: number;
  searchName: string; // Add search state
  statusFilter: string; // Add status filter state
  error: string | null;
};

const initialState: ExtendedInitialState = {
  loading: true, // Start with loading true to show loading initially
  updating: false,
  updateSuccess: false,
  entity: null,
  entities: [],
  successMessage: null,
  allEntities: [],
  organizations: [],
  allOrganizations: [], // Initialize all organizations for dropdown
  currentOrganization: null,
  total: 0,
  totalPages: 0, // Initialize total pages
  page: 1,
  limit: 10, // Set default to 2 as per your API example
  searchName: '', // Initialize search state
  statusFilter: 'all', // Matches statusFilterOptions "All Status" value
  error: null,
};

export const fetchOrganizations = createAsyncThunk(
  'organizations/fetchOrganizations',
  async (params: OrganizationParams, { rejectWithValue }) => {
    try {
      return await organizationService.fetchOrganizations(params);
    } catch (error) {
      return rejectWithValue(error);
    }
  }
);

export const fetchAllOrganizations = createAsyncThunk(
  'organizations/fetchAllOrganizations',
  async (_, { rejectWithValue }) => {
    try {
      return await organizationService.fetchAllOrganizations();
    } catch (error) {
      return rejectWithValue(error);
    }
  }
);

export const fetchOrganizationById = createAsyncThunk(
  'organizations/fetchOrganizationById',
  async (id: string, { rejectWithValue }) => {
    try {
      return await organizationService.fetchOrganizationById(id);
    } catch (error) {
      return rejectWithValue(error);
    }
  }
);

export const createOrganization = createAsyncThunk(
  'organizations/createOrganization',
  async (data: Organization, { rejectWithValue }) => {
    try {
      return await organizationService.createOrganization(data);
    } catch (error) {
      return rejectWithValue(error);
    }
  }
);

export const toggleOrganizationStatus = createAsyncThunk(
  'organizations/toggleStatus',
  async (id: string, { rejectWithValue }) => {
    try {
      return await organizationService.toggleOrganizationStatus(id);
    } catch (error) {
      return rejectWithValue(error);
    }
  }
);

export const updateOrganization = createAsyncThunk(
  'organizations/updateOrganization',
  async (data: Organization, { rejectWithValue }) => {
    try {
      return await organizationService.updateOrganization(data);
    } catch (error) {
      return rejectWithValue(error);
    }
  }
);

export const deleteOrganization = createAsyncThunk(
  'organizations/deleteOrganization',
  async (id: string, { rejectWithValue }) => {
    try {
      await organizationService.deleteOrganization(id);
      return id;
    } catch (error) {
      return rejectWithValue(error);
    }
  }
);

const organizationSlice = createSlice({
  name: 'organizations',
  initialState,
  reducers: {
    setSearchName: (state, action) => {
      state.searchName = action.payload;
      state.page = 1; // Reset to first page when searching
    },
    setStatusFilter: (state, action) => {
      state.statusFilter = action.payload;
      state.page = 1; // Reset to first page when filtering
    },
    setPage: (state, action) => {
      state.page = action.payload;
    },
    setPageSize: (state, action) => {
      state.limit = action.payload;
      state.page = 1; // Reset to first page when changing page size
    },
    resetFilters: (state) => {
      state.searchName = '';
      state.statusFilter = 'all'; // Reset to "All Status" option
      state.page = 1;
    },
    clearMessages: (state) => {
      state.errorMessage = null;
      state.successMessage = null;
    },
    clearSuccessMessage: (state) => {
      state.successMessage = null;
    },
    clearUpdateSuccess: (state) => {
      state.updateSuccess = false;
      state.successMessage = null;
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(fetchOrganizations.fulfilled, (state, action) => {
        state.organizations = action?.payload?.data || [];
        state.total = action?.payload?.total || 0;
        state.totalPages = action?.payload?.totalPages || 0;
        state.page = action?.payload?.page || state.page;
        state.limit = action?.payload?.pageSize || state.limit;
        state.loading = false;
        state.error = null;
      })
      .addCase(fetchAllOrganizations.fulfilled, (state, action) => {
        state.allOrganizations = action?.payload?.data || [];
        state.loading = false;
        state.error = null;
      })
      .addCase(fetchOrganizationById.fulfilled, (state, action) => {
        state.currentOrganization = action.payload;
        state.loading = false;
        state.error = null;
      })
      .addCase(updateOrganization.fulfilled, (state, action) => {
        state.updating = false;
        state.updateSuccess = true;
        state.successMessage = 'Organization updated successfully';
        state.errorMessage = null;

        // Update the organization in both lists
        const updatedOrg = action.payload;
        if (updatedOrg) {
          // Ensure the updated organization has the correct status field
          const normalizedOrg = {
            ...updatedOrg,
            status: updatedOrg.isActive ? 'active' : 'inactive',
          };

          // Update in paginated organizations list
          const orgIndex = state.organizations.findIndex(
            (org) => org.id === normalizedOrg.id
          );
          if (orgIndex !== -1) {
            state.organizations[orgIndex] = normalizedOrg;
          }

          // Update in all organizations list (for dropdown)
          const allOrgIndex = state.allOrganizations.findIndex(
            (org) => org.id === normalizedOrg.id
          );
          if (allOrgIndex !== -1) {
            state.allOrganizations[allOrgIndex] = normalizedOrg;
          } else {
            // If not found, add it to the allOrganizations array
            state.allOrganizations.push(normalizedOrg);
          }
        }
      })
      .addCase(updateOrganization.rejected, (state, action) => {
        state.updating = false;
        state.updateSuccess = false;
        const error = action.payload as ApiError;

        // Extract error message with multiple fallback strategies
        let errorMessage = 'Failed to update organization. Please try again.';

        if (error?.response?.data) {
          const responseData = error.response.data as any;

          // Strategy 1: Check if data itself is a string
          if (typeof responseData === 'string') {
            errorMessage = responseData;
          }
          // Strategy 2: Check for message property
          else if (responseData?.message) {
            errorMessage = responseData.message;
          }
          // Strategy 3: Check for error property
          else if (responseData?.error) {
            errorMessage = responseData.error;
          }
          // Strategy 4: Check for errorMessage property
          else if (responseData?.errorMessage) {
            errorMessage = responseData.errorMessage;
          }
        }
        // Strategy 5: Fall back to error.message
        else if (error?.message) {
          errorMessage = error.message;
        }

        state.error = errorMessage;
        state.successMessage = null;
      })
      .addCase(createOrganization.fulfilled, (state, action) => {
        state.updating = false;
        state.updateSuccess = true;
        state.successMessage =
          'Organization created successfully! A confirmation email has been sent to the registered email address.';
        state.errorMessage = null;

        // Add the new organization to both lists if returned by API
        const newOrg = action.payload;
        if (newOrg) {
          // Ensure the new organization has the correct status field
          const normalizedOrg = {
            ...newOrg,
            status: newOrg.isActive ? 'active' : 'inactive',
          };

          // Add to all organizations list (for dropdown)
          const exists = state.allOrganizations.some(
            (org) => org.id === normalizedOrg.id
          );
          if (!exists) {
            state.allOrganizations = [...state.allOrganizations, normalizedOrg];
          }

          // Also add to the current organizations list if it's not already there
          const existsInCurrent = state.organizations.some(
            (org) => org.id === normalizedOrg.id
          );
          if (!existsInCurrent) {
            state.organizations = [normalizedOrg, ...state.organizations];
          }
        }
      })
      .addCase(createOrganization.rejected, (state, action) => {
        state.updating = false;
        state.updateSuccess = false;
        const error = action.payload as ApiError;

        // Debug logging to understand error structure
        console.log('=== CREATE ORGANIZATION ERROR DEBUG ===');
        console.log('Full error object:', error);
        console.log('error.response:', error?.response);
        console.log('error.response.data:', error?.response?.data);
        console.log(
          'Type of error.response.data:',
          typeof error?.response?.data
        );
        console.log(
          'error.response.data.message:',
          error?.response?.data?.message
        );
        console.log('error.message:', error?.message);
        console.log('======================================');

        // Extract error message with multiple fallback strategies
        let errorMessage = 'Failed to create organization. Please try again.';

        if (error?.response?.data) {
          const responseData = error.response.data as any;

          // Strategy 1: Check if data itself is a string
          if (typeof responseData === 'string') {
            errorMessage = responseData;
          }
          // Strategy 2: Check for message property
          else if (responseData?.message) {
            errorMessage = responseData.message;
          }
          // Strategy 3: Check for error property
          else if (responseData?.error) {
            errorMessage = responseData.error;
          }
          // Strategy 4: Check for errorMessage property
          else if (responseData?.errorMessage) {
            errorMessage = responseData.errorMessage;
          }
        }
        // Strategy 5: Fall back to error.message
        else if (error?.message) {
          errorMessage = error.message;
        }

        state.error = errorMessage;
        state.successMessage = null;
      })
      .addCase(deleteOrganization.fulfilled, (state, action) => {
        state.updating = false;
        state.updateSuccess = true;
        state.successMessage = 'Organization deleted successfully';
        state.errorMessage = null;

        // Remove the organization from both lists
        const deletedOrgId = action.meta.arg; // The ID passed to the thunk
        if (deletedOrgId) {
          // Remove from paginated organizations list
          state.organizations = state.organizations.filter(
            (org) => org.id !== deletedOrgId
          );

          // Remove from all organizations list (for dropdown)
          state.allOrganizations = state.allOrganizations.filter(
            (org) => org.id !== deletedOrgId
          );

          // Update total count
          state.total = Math.max(0, state.total - 1);
        }
      })
      .addCase(deleteOrganization.rejected, (state, action) => {
        state.updating = false;
        state.updateSuccess = false;
        const error = action.payload as ApiError;
        state.error =
          (typeof error?.response?.data === 'string'
            ? error.response.data
            : error?.response?.data?.message) ||
          error?.message ||
          'Failed to delete organization. Please try again.';
        state.successMessage = null;
      })
      .addMatcher(
        isPending(
          fetchOrganizations,
          fetchAllOrganizations,
          fetchOrganizationById
        ),
        (state) => {
          state.loading = true;
          state.error = null;
          // Clear existing data when starting a new fetch to prevent showing old data
          if (state.organizations.length > 0) {
            state.organizations = [];
            state.total = 0;
          }
        }
      )
      .addMatcher(
        isPending(createOrganization, updateOrganization, deleteOrganization),
        (state) => {
          state.updating = true;
          state.updateSuccess = false;
          state.errorMessage = null;
          state.successMessage = null;
        }
      )
      .addMatcher(
        isRejected(
          fetchOrganizations,
          fetchAllOrganizations,
          fetchOrganizationById
        ),
        (state, action) => {
          // Don't set loading to false if the request was cancelled (duplicate request)
          const error = action.payload as any;
          if (
            error?.name === 'RequestCancelledError' ||
            error?.message === 'Request was cancelled'
          ) {
            return; // Keep loading state as is for cancelled requests
          }

          if (action.type === 'organizations/fetchOrganizations/rejected') {
            const requestParams = action.meta.arg;

            const isParamsObject =
              requestParams &&
              typeof requestParams === 'object' &&
              !Array.isArray(requestParams);

            const requestSearchTerm = isParamsObject
              ? requestParams.name || ''
              : '';
            const requestStatus = isParamsObject
              ? requestParams.status || 'all'
              : 'all';
            const requestPage = isParamsObject ? requestParams.page || 1 : 1;

            const currentSearchTerm = state.searchName || '';
            const currentStatus = state.statusFilter || 'all';
            const currentPage = state.page || 1;

            if (
              requestSearchTerm !== currentSearchTerm ||
              requestStatus !== currentStatus ||
              requestPage !== currentPage
            ) {
              return;
            }
          }

          state.loading = false;
          const apiError = error as ApiError;
          state.error =
            (typeof apiError?.response?.data === 'string'
              ? apiError.response.data
              : apiError?.response?.data?.message) ||
            apiError?.message ||
            'Failed to load organizations. Please try again.';
        }
      )
      // Reset slice on logout
      .addMatcher(
        (action) => action.type === 'RESET_ALL_SLICES',
        () => initialState
      );
  },
});

// Export actions
export const {
  setSearchName,
  setStatusFilter,
  setPage,
  setPageSize,
  resetFilters,
  clearMessages,
  clearSuccessMessage,
  clearUpdateSuccess,
} = organizationSlice.actions;

export default organizationSlice.reducer;
