import { X } from 'lucide-react';
import React, { useEffect, useState } from 'react';

import AppButton from '../../../components/Common/AppButton';
import Modal from '../../../components/Common/Modal';
import MUISelect from '../../../components/Common/MUISelect';
import organizationService from '../../../store/features/organizations/organization.service';

interface AddToOrganizationModalProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: (
    organizationId: string,
    subscriptionIds: string[]
  ) => Promise<void>;
  selectedSubscribers: Array<{
    id: string;
    name: string;
    email: string;
    requestedClinicName?: string;
  }>;
  loading?: boolean;
}

const AddToOrganizationModal: React.FC<AddToOrganizationModalProps> = ({
  isOpen,
  onClose,
  onConfirm,
  selectedSubscribers,
  loading = false,
}) => {
  const [selectedOrganization, setSelectedOrganization] = useState('');
  const [organizations, setOrganizations] = useState<
    Array<{ label: string; value: string }>
  >([]);
  const [loadingOrganizations, setLoadingOrganizations] = useState(false);
  const [internalSelectedSubscribers, setInternalSelectedSubscribers] =
    useState(selectedSubscribers);

  useEffect(() => {
    setInternalSelectedSubscribers(selectedSubscribers);
  }, [selectedSubscribers]);

  useEffect(() => {
    const fetchOrganizations = async () => {
      if (!isOpen) return;

      setLoadingOrganizations(true);
      try {
        const response = await organizationService.fetchAllOrganizations({
          isClinicOrganization: true,
        });
        const orgOptions = response.data.map((org: any) => ({
          label: org.name,
          value: org.id || org._id,
        }));
        setOrganizations(orgOptions);
      } catch (error) {
        console.error('Failed to fetch organizations:', error);
      } finally {
        setLoadingOrganizations(false);
      }
    };

    fetchOrganizations();
  }, [isOpen]);

  const handleRemoveSubscriber = (subscriberId: string) => {
    setInternalSelectedSubscribers((prev) =>
      prev.filter((sub) => sub.id !== subscriberId)
    );
  };

  const handleConfirm = async () => {
    if (!selectedOrganization || internalSelectedSubscribers.length === 0)
      return;

    const subscriptionIds = internalSelectedSubscribers.map((sub) => sub.id);
    await onConfirm(selectedOrganization, subscriptionIds);

    // Reset form after successful operation
    setSelectedOrganization('');
    setInternalSelectedSubscribers([]);
  };

  const handleClose = () => {
    if (!loading) {
      setSelectedOrganization('');
      setInternalSelectedSubscribers([]);
      onClose();
    }
  };

  const isConfirmDisabled =
    !selectedOrganization ||
    internalSelectedSubscribers.length === 0 ||
    loading;

  return (
    <Modal
      isOpen={isOpen}
      onClose={handleClose}
      title='Add to Organization'
      size='md'
    >
      <div className='space-y-4'>
        {/* Organization Dropdown */}
        <div>
          <label className='block text-sm font-medium text-gray-700 mb-2'>
            Select Organization
          </label>
          <MUISelect
            value={selectedOrganization}
            onChange={(e) => setSelectedOrganization(e.target.value)}
            options={organizations}
            placeholder={
              loadingOrganizations
                ? 'Loading organizations...'
                : 'Choose organization'
            }
            disabled={loadingOrganizations || loading}
            sx={{ width: '100%' }}
          />
        </div>

        {/* Selected Subscribers List */}
        <div>
          <label className='block text-sm font-medium text-gray-700 mb-2'>
            Selected Subscribers ({internalSelectedSubscribers.length})
          </label>
          <div className='max-h-60 overflow-y-auto space-y-2 border border-gray-200 rounded-lg p-3'>
            {internalSelectedSubscribers.length === 0 ? (
              <p className='text-gray-500 text-sm'>No subscribers selected</p>
            ) : (
              internalSelectedSubscribers.map((subscriber) => (
                <div
                  key={subscriber.id}
                  className='flex items-center justify-between bg-gray-50 p-3 rounded-lg'
                >
                  <div className='flex-1 min-w-0'>
                    <p className='text-sm font-medium text-gray-900 truncate'>
                      {subscriber.name}
                    </p>
                    <p className='text-sm text-gray-500 truncate'>
                      {subscriber.email}
                    </p>
                    {subscriber.requestedClinicName && (
                      <p className='text-xs text-blue-600 truncate'>
                        Requested: {subscriber.requestedClinicName}
                      </p>
                    )}
                  </div>
                  <button
                    onClick={() => handleRemoveSubscriber(subscriber.id)}
                    className='ml-2 p-1 text-gray-400 hover:text-red-600 transition-colors'
                    title='Remove subscriber'
                  >
                    <X className='w-4 h-4' />
                  </button>
                </div>
              ))
            )}
          </div>
        </div>

        {/* Action Buttons */}
        <div className='flex justify-end space-x-3 pt-4 border-t'>
          <AppButton onClick={handleClose} disabled={loading} kind='secondary'>
            Cancel
          </AppButton>
          <AppButton
            onClick={handleConfirm}
            disabled={isConfirmDisabled}
            kind='primary'
            startIcon={
              loading ? (
                <div className='w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin' />
              ) : null
            }
          >
            {loading ? 'Adding...' : 'Add to Organization'}
          </AppButton>
        </div>
      </div>
    </Modal>
  );
};

export default AddToOrganizationModal;
