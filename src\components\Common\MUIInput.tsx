import TextField, { TextFieldProps } from '@mui/material/TextField';
import React from 'react';

type TextInputProps = {
  label?: string;
} & TextFieldProps;

const TextInput = React.forwardRef<HTMLDivElement, TextInputProps>(
  ({ label, ...props }, ref) => (
    <TextField
      fullWidth
      label={label}
      size='small'
      variant='outlined'
      inputRef={ref}
      {...props}
    />
  )
);

TextInput.displayName = 'TextInput';

export default TextInput;
