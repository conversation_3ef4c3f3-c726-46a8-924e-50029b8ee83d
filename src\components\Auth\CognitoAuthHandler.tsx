/**
 * Cognito Authentication Handler
 * Handles Cognito authentication state and user data fetching
 * Similar to MsalAuthHandler but for AWS Cognito
 */

import React, { useCallback, useEffect, useState } from 'react';
import { useDispatch } from 'react-redux';
import { useLocation, useNavigate } from 'react-router-dom';

import { PATHS } from '../../constants/paths';
import { ROLES } from '../../constants/roles';
import {
  setAuthState,
  setEmrUserInfo,
  setLoading,
} from '../../store/features/auth/auth.slice';
import userService from '../../store/features/users/user.service';
import { User } from '../../types';
import {
  getCognitoUserInfo,
  isCognitoAuthenticated,
} from '../../utils/cognitoAuth';
import { handlePostLoginRedirect } from '../../utils/redirect-utils';
import LoadingSpinner from '../Common/LoadingSpinner';

const CognitoAuthHandler: React.FC<{ children: React.ReactNode }> = ({
  children,
}) => {
  const navigate = useNavigate();
  const location = useLocation();
  const pathname = location.pathname;
  const dispatch = useDispatch();
  const [isAuthenticating, setIsAuthenticating] = useState(false);
  const [isInitialized, setIsInitialized] = useState(false);
  const [hasFetchedUser, setHasFetchedUser] = useState(false);
  const [fetchAttempted, setFetchAttempted] = useState(false);

  const fetchAndSetUser = useCallback(async () => {
    // Prevent multiple fetch attempts
    if (fetchAttempted) {
      console.log('CognitoAuthHandler: Fetch already attempted, skipping');
      return null;
    }

    try {
      setIsAuthenticating(true);
      dispatch(setLoading(true)); // Use Redux loading state
      setFetchAttempted(true);

      // Get Cognito user info
      const cognitoUser = await getCognitoUserInfo();

      if (!cognitoUser || !cognitoUser.email) {
        console.warn('CognitoAuthHandler: No Cognito user info available');
        return null;
      }

      // Fetch EMR user info
      const emrUser = await userService.fetchUserByEmail(cognitoUser.email);

      if (emrUser) {
        // Map EMR user role to our expected role names
        const mapRoleName = (role: string): string => {
          const normalized = role.toLowerCase().trim();
          if (normalized.includes('organization')) {
            return ROLES.ORGANIZATION_ADMIN;
          }
          if (
            normalized === 'super admin' ||
            normalized === 'super_admin' ||
            normalized === 'superadmin'
          ) {
            return ROLES.SUPER_ADMIN;
          }
          if (normalized === 'admin' || normalized === 'administrator') {
            return ROLES.ORGANIZATION_ADMIN;
          }
          return role;
        };

        const mappedRoleName = mapRoleName(emrUser.userRole);

        // Map EMR user to our User type
        const user: User = {
          id: emrUser.id,
          email: emrUser.email,
          firstName: emrUser.name?.split(' ')[0] || '',
          lastName: emrUser.name?.split(' ').slice(1).join(' ') || '',
          name: emrUser.name,
          phone: emrUser.phoneNumber || '',
          organizationName: emrUser.organizationName,
          roles: [
            {
              id: emrUser.roleId,
              name: mappedRoleName,
              description: emrUser.userRole,
              permissions:
                emrUser.permissionKeys?.map((key: string) => ({
                  id: key,
                  key,
                  api: `/${key.replace(/\./g, '/')}`,
                  methods: ['GET', 'POST', 'PUT', 'DELETE'],
                  module:
                    (key.split('.')[0] as 'EMR' | 'MRD' | 'ADMIN') || 'ADMIN',
                  actions: ['read'],
                })) || [],
              organizationId: emrUser.organizationId,
              isSystem: true,
              departmentId: '',
              isDefault: false,
              createdAt: new Date(),
            },
          ],
          userRole: emrUser.userRole,
          organizationId: emrUser.organizationId,
          status: emrUser.isActive ? 'active' : 'inactive',
          mustResetPassword: false,
          createdAt: new Date(emrUser.createdAt || new Date()),
          lastLogin: new Date(emrUser.lastLogin || new Date()),
          subscriptionExpiryDate: emrUser.subscriptionExpiryDate
            ? new Date(emrUser.subscriptionExpiryDate)
            : null,
          subscriberId: emrUser.subscriberId || emrUser.organizationId,
        };

        // Save user and emrUserInfo to sessionStorage for persistence
        sessionStorage.setItem('user', JSON.stringify(user));
        sessionStorage.setItem('emrUserInfo', JSON.stringify(emrUser));

        // Update auth state with the new user and EMR info
        dispatch(setEmrUserInfo(emrUser));
        await dispatch(
          setAuthState({
            user: {
              ...user,
              subscriptionExpiryDate: emrUser.subscriptionExpiryDate
                ? new Date(emrUser.subscriptionExpiryDate)
                : null,
              subscriberId: emrUser.subscriberId || emrUser.organizationId,
            },
            selectedOrganization: null,
            currentOrganization: null,
          })
        );

        console.log(
          'CognitoAuthHandler: User data fetched and stored successfully'
        );

        // Only handle redirect if we're on the login/root path
        if (pathname === PATHS.LOGIN || pathname === PATHS.ROOT) {
          handlePostLoginRedirect(navigate, pathname);
        }

        setHasFetchedUser(true);
        return user;
      } else {
        console.warn(
          'CognitoAuthHandler: No user data received from EMR service'
        );
        return null;
      }
    } catch (error) {
      console.error('CognitoAuthHandler: Error fetching user info:', error);

      // Clear auth state on error
      const emptyUser: User = {
        id: '',
        email: '',
        name: '',
        firstName: '',
        lastName: '',
        roles: [],
        userRole: '',
        organizationId: '',
        status: 'inactive',
        mustResetPassword: false,
        createdAt: new Date(),
        lastLogin: new Date(),
        phone: '',
        subscriptionExpiryDate: null,
      };

      dispatch(
        setAuthState({
          user: emptyUser,
          selectedOrganization: null,
          currentOrganization: null,
        })
      );

      // Redirect to unauthorized page when fetchUserByEmail fails
      // BUT ONLY if we are NOT on the landing page or already on the unauthorized page
      if (
        pathname !== PATHS.LOGIN &&
        pathname !== PATHS.ROOT &&
        pathname !== '/unauthorized'
      ) {
        console.log(
          'CognitoAuthHandler: Redirecting to unauthorized page due to fetch error'
        );
        navigate('/unauthorized?reason=user_fetch_failed', { replace: true });
      }

      return null;
    } finally {
      setIsAuthenticating(false);
      dispatch(setLoading(false)); // Use Redux loading state
    }
  }, [dispatch, navigate, pathname, fetchAttempted]);

  // Function to refresh EMR user info (for non-super admin users on navigation)
  const refreshEmrUserInfo = useCallback(async () => {
    try {
      // Get Cognito user info
      const cognitoUser = await getCognitoUserInfo();

      if (!cognitoUser || !cognitoUser.email) {
        return;
      }

      // Check if user is super admin from sessionStorage
      const userRole = sessionStorage.getItem('userRole');
      const isSuperAdmin =
        userRole === ROLES.SUPER_ADMIN || userRole === 'Super Admin';

      // Only refresh for non-super admin users
      if (isSuperAdmin) {
        return;
      }

      // Fetch EMR user info
      const emrUser = await userService.fetchUserByEmail(cognitoUser.email);

      if (emrUser) {
        // Update emrUserInfo in Redux and sessionStorage
        dispatch(setEmrUserInfo(emrUser));

        sessionStorage.setItem('emrUserInfo', JSON.stringify(emrUser));
      }
    } catch (error) {
      console.error(
        'CognitoAuthHandler: Error refreshing EMR user info:',
        error
      );
      // Don't throw error, just log it - this is a background refresh
    }
  }, [dispatch]);

  useEffect(() => {
    const initAuth = async () => {
      try {
        const urlParams = new URLSearchParams(window.location.search);
        const hasCode = urlParams.has('code');
        const hasState = urlParams.has('state');
        const hasError = urlParams.has('error');

        // Check if LandingPage is currently processing the callback
        if (
          (hasCode && hasState) ||
          hasError ||
          sessionStorage.getItem('cognito_processing_callback')
        ) {
          console.log(
            'CognitoAuthHandler: Callback processing in progress elsewhere, pausing init...'
          );
          // Don't set isInitialized = true yet, keep showing global loader OR render children if at login
          return;
        }

        // Check if user is already authenticated in Redux
        // If we have a user in Redux, we're likely good
        const storedUserStr = sessionStorage.getItem('user');

        // Check if user is authenticated with Cognito
        const authenticated = await isCognitoAuthenticated();

        if (authenticated) {
          if (storedUserStr) {
            try {
              const storedUser = JSON.parse(storedUserStr);
              // Hydrate Redux from localStorage if needed
              dispatch(
                setAuthState({
                  user: storedUser,
                  selectedOrganization: null,
                  currentOrganization: null,
                })
              );
              setHasFetchedUser(true);
              setFetchAttempted(true);

              // Refresh EMR user info to get latest organization name
              await refreshEmrUserInfo();
            } catch (e) {
              console.error('Failed to parse stored user', e);
              await fetchAndSetUser();
            }
          } else {
            console.log(
              'CognitoAuthHandler: User is authenticated but no local profile, fetching...'
            );
            await fetchAndSetUser();
          }
        } else {
          console.log('CognitoAuthHandler: User not authenticated');
          // If not on login page and not authenticated, redirect to login
          if (pathname !== PATHS.LOGIN && pathname !== PATHS.ROOT) {
            sessionStorage.setItem('preLoginUrl', pathname);
            navigate(PATHS.LOGIN, { replace: true });
          }
        }
      } catch (error) {
        console.error(
          'CognitoAuthHandler: Error during initialization:',
          error
        );
      } finally {
        // Only set initialized if we're not waiting for a callback.
        // If we are waiting, we want to stay in the 'processing' state.
        const urlParams = new URLSearchParams(window.location.search);
        if (
          !urlParams.has('code') &&
          !urlParams.has('error') &&
          !sessionStorage.getItem('cognito_processing_callback')
        ) {
          setIsInitialized(true);
        }
      }
    };

    if (!isInitialized) {
      initAuth();
    } else if (
      pathname !== PATHS.LOGIN &&
      pathname !== PATHS.ROOT &&
      !hasFetchedUser &&
      !isAuthenticating &&
      !fetchAttempted
    ) {
      // Re-check on navigation if we haven't succeeded yet
      isCognitoAuthenticated().then((auth) => {
        if (auth) fetchAndSetUser();
      });
    }
  }, [
    fetchAndSetUser,
    navigate,
    pathname,
    isInitialized,
    hasFetchedUser,
    isAuthenticating,
    fetchAttempted,
    dispatch,
    refreshEmrUserInfo,
  ]);

  // Refresh EMR user info on navigation for non-super admin users
  useEffect(() => {
    // Only refresh if user is authenticated and initialized
    if (isInitialized && hasFetchedUser && !isAuthenticating) {
      // Don't refresh on login/root or unauthorized paths
      if (
        pathname !== PATHS.LOGIN &&
        pathname !== PATHS.ROOT &&
        pathname !== '/unauthorized'
      ) {
        refreshEmrUserInfo();
      }
    }
  }, [
    pathname,
    isInitialized,
    hasFetchedUser,
    isAuthenticating,
    refreshEmrUserInfo,
  ]);

  // Show loading state while authenticating or initializing
  // BUT: If we are on the login/root path, we should always render children
  // so the LandingPage can show its own loader and handle the callback.
  if (!isInitialized || isAuthenticating) {
    if (pathname === PATHS.LOGIN || pathname === PATHS.ROOT) {
      return <>{children}</>;
    }
    return (
      <div className='flex flex-col items-center justify-center min-h-screen bg-gray-50'>
        <LoadingSpinner />
      </div>
    );
  }

  return <>{children}</>;
};

export default CognitoAuthHandler;
