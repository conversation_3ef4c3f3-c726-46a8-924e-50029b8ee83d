import { cognitoConfig } from '../../../utils/cognitoConfig';

function parseJwt(token: string) {
  try {
    const base64Url = token.split('.')[1];
    if (!base64Url) return null;
    const base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/');
    const jsonPayload = decodeURIComponent(
      atob(base64)
        .split('')
        .map((c) => `%${`00${c.charCodeAt(0).toString(16)}`.slice(-2)}`)
        .join('')
    );
    return JSON.parse(jsonPayload);
  } catch (e) {
    return null;
  }
}

/**
 * Handles OAuth callback from Cognito with robust error handling
 *
 * CRITICAL FIX for "Invalid challenge transition" error:
 * - Validates state parameter to ensure session isolation
 * - Clears old Cognito data before storing new tokens
 * - Provides detailed error messages for debugging
 */
export async function handleCognitoCallback(searchParams: URLSearchParams) {
  const code = searchParams.get('code');
  const state = searchParams.get('state');
  const error = searchParams.get('error');
  const errorDescription = searchParams.get('error_description');

  // Check for OAuth errors from Cognito
  if (error) {
    console.error('Admin: Cognito OAuth error:', { error, errorDescription });

    // Clear any stale session data
    const keysToClear = ['Cognito', 'amplify', 'cognito'];
    keysToClear.forEach((token) => {
      Object.keys(sessionStorage)
        .filter((key) => key.toLowerCase().includes(token.toLowerCase()))
        .forEach((key) => sessionStorage.removeItem(key));
    });

    throw new Error(
      `Authentication failed: ${errorDescription || error}. Please try logging in again.`
    );
  }

  if (!code) {
    throw new Error('No authorization code found in URL');
  }

  // CRITICAL: Validate state to prevent CSRF and ensure session isolation
  const savedState = sessionStorage.getItem('cognito_state');

  if (!savedState) {
    console.error('Admin: No saved state found in session storage');
    throw new Error('Session expired or invalid. Please try logging in again.');
  }

  if (state !== savedState) {
    console.error('Admin: State mismatch!', {
      received: state,
      expected: savedState,
      message:
        'This usually happens when logging in from multiple tabs or apps simultaneously',
    });

    // Clear stale session data
    sessionStorage.removeItem('cognito_verifier');
    sessionStorage.removeItem('cognito_state');

    throw new Error(
      'Authentication state mismatch. This can happen when logging in from multiple tabs. Please close other tabs and try again.'
    );
  }

  const verifier = sessionStorage.getItem('cognito_verifier');

  if (!verifier) {
    console.error('Admin: No PKCE verifier found in session storage');
    throw new Error('Session expired or invalid. Please try logging in again.');
  }

  const domain = cognitoConfig.cognitoDomain.replace(/^https?:\/\//, '');
  const body = new URLSearchParams({
    grant_type: 'authorization_code',
    client_id: cognitoConfig.clientId,
    code,
    redirect_uri: cognitoConfig.redirectUri,
    code_verifier: verifier,
  });

  const response = await fetch(`https://${domain}/oauth2/token`, {
    method: 'POST',
    headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
    body,
  });

  if (!response.ok) {
    const err = await response.text();
    console.error('Admin: Token exchange failed:', err);

    // Clear stale session data
    sessionStorage.removeItem('cognito_verifier');
    sessionStorage.removeItem('cognito_state');

    throw new Error(
      `Token exchange failed: ${err}. Please try logging in again.`
    );
  }

  const tokens = await response.json();

  // Decode IdToken to get username (sub or cognito:username)
  const idTokenPayload = parseJwt(tokens.id_token);
  const username = idTokenPayload?.sub || idTokenPayload?.['cognito:username'];

  if (!username) {
    throw new Error('Could not determine username from token');
  }

  // Clear any old Cognito data before storing new tokens
  const clientId = cognitoConfig.clientId;
  const prefix = `CognitoIdentityServiceProvider.${clientId}`;

  // Remove old user data
  const oldLastAuthUser = sessionStorage.getItem(`${prefix}.LastAuthUser`);
  if (oldLastAuthUser) {
    sessionStorage.removeItem(`${prefix}.${oldLastAuthUser}.idToken`);
    sessionStorage.removeItem(`${prefix}.${oldLastAuthUser}.accessToken`);
    sessionStorage.removeItem(`${prefix}.${oldLastAuthUser}.refreshToken`);
    sessionStorage.removeItem(`${prefix}.${oldLastAuthUser}.clockDrift`);
  }

  // Store in sessionStorage using Amplify v6 compatible keys
  sessionStorage.setItem(`${prefix}.LastAuthUser`, username);
  sessionStorage.setItem(`${prefix}.${username}.idToken`, tokens.id_token);
  sessionStorage.setItem(
    `${prefix}.${username}.accessToken`,
    tokens.access_token
  );
  sessionStorage.setItem(
    `${prefix}.${username}.refreshToken`,
    tokens.refresh_token
  );
  sessionStorage.setItem(`${prefix}.${username}.clockDrift`, '0');

  // Clear PKCE verifier and state
  sessionStorage.removeItem('cognito_verifier');
  sessionStorage.removeItem('cognito_state');

  console.log('Admin: Successfully authenticated and stored tokens');
  return tokens;
}
