import { useSelector } from 'react-redux';

import { isOrgAdminRole, isSuperAdminRole } from '../constants/roles';
import { RootState } from '../store';
import { PermissionAction, UserRole } from '../types';

export const useAuth = () => {
  const authState = useSelector((state: RootState) => state.auth);

  const {
    user,
    selectedOrganization,
    currentOrganization,
    isAuthenticated,
    loading,
    error,
    loggingOut,
  } = authState;

  // Check for super admin (preferred from state, fallback to sessionStorage for tab isolation)
  const isSuperAdmin =
    user?.roles?.some((role) => isSuperAdminRole(role.name)) ||
    isSuperAdminRole(sessionStorage.getItem('userRole')) ||
    false;

  // Check for organization admin
  const isOrganizationAdmin =
    user?.roles?.some((role) => isOrgAdminRole(role.name)) ||
    isOrgAdminRole(sessionStorage.getItem('userRole')) ||
    false;

  // Get the user's organization ID
  const userOrganizationId = user?.organizationId;

  // Check if user has access to a specific organization
  const hasOrganizationAccess = (orgId: string | undefined): boolean => {
    if (isSuperAdmin) return true; // Super admins have access to all orgs
    if (!orgId || !userOrganizationId) return false;
    return orgId === userOrganizationId; // Regular admins only have access to their org
  };

  const hasRole = (roleName: UserRole): boolean => {
    if (isSuperAdmin) return true;
    const roleNameUpper = roleName.toUpperCase();
    return (
      user?.roles.some(
        (role) =>
          role.name.toUpperCase() === roleNameUpper ||
          role.name.toLowerCase() === roleName.toLowerCase()
      ) || false
    );
  };

  const hasPermission = (
    module: string,
    feature: string,
    action: string
  ): boolean => {
    if (isSuperAdmin) return true;

    return (
      user?.roles.some((role) =>
        role.permissions.some(
          (permission) =>
            permission.module === module &&
            permission.feature === feature &&
            permission.actions?.includes(action as PermissionAction)
        )
      ) || false
    );
  };

  const getDisplayName = (): string => {
    if (!user) return '';
    return `${user.firstName} ${user.lastName}`;
  };

  const getCurrentOrganization = () => {
    return selectedOrganization;
  };

  const isSubscriptionOrganization = (): boolean => {
    const emrUserInfo = authState.emrUserInfo;

    if (emrUserInfo?.organizationName === 'Subscription - Organization') {
      return true;
    }

    if (user?.organizationName === 'Subscription - Organization') {
      return true;
    }

    try {
      const emrUserInfoStr = sessionStorage.getItem('emrUserInfo');
      if (emrUserInfoStr) {
        const emrUserInfoObj = JSON.parse(emrUserInfoStr);
        if (
          emrUserInfoObj?.organizationName === 'Subscription - Organization'
        ) {
          return true;
        }
      }
    } catch {
      // Ignore JSON parsing errors
    }

    return false;
  };

  return {
    user,
    selectedOrganization,
    currentOrganization,
    isAuthenticated,
    isSuperAdmin,
    isOrganizationAdmin,
    isOrganizationUser: !isSuperAdmin && isOrganizationAdmin,
    userOrganizationId,
    loading,
    error,
    loggingOut,
    hasRole,
    hasPermission,
    hasOrganizationAccess,
    getDisplayName,
    getCurrentOrganization,
    isSubscriptionOrganization,
    emrUserInfo: authState.emrUserInfo,
  };
};
