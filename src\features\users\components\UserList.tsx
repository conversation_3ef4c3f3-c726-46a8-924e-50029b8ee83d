import { Edit, Plus, Trash2, User as UserIcon } from 'lucide-react';
import React, { memo, useCallback, useMemo, useState } from 'react';

import AppButton from '../../../components/Common/AppButton';
import ConfirmationModal from '../../../components/Common/ConfirmationModal';
import { Column } from '../../../components/Common/DataTable';
import Modal from '../../../components/Common/Modal';
import SelectInput from '../../../components/Common/MUISelect';
import SafeDataTable from '../../../components/Common/SafeDataTable';
import SearchBar from '../../../components/Common/SearchBar';
import StatusBadge from '../../../components/Common/StatusBadge';
import { statusFilterOptions } from '../../../constants/status-options';
import { useAuth } from '../../../hooks/useAuth';
import { useConfirmation } from '../../../hooks/useConfirmation';
import {
  CreateUserData,
  UpdateUserData,
} from '../../../store/features/users/user.service';
import { useRoles } from '../../roles/hooks/useRoles';
import { useUsers } from '../hooks/useUsers';
import { UserFormSchema } from '../schemas/user.schema';
import { User } from '../types/user.types';
import UserForm from './UserForm';

interface UserListProps {
  organizationName: string;
  organizationId?: string | null;
  isClinicOrganization?: boolean | undefined;
}

const UserList: React.FC<UserListProps> = memo(
  ({ organizationName, organizationId, isClinicOrganization }) => {
    const [isFormOpen, setIsFormOpen] = useState(false);
    const [userToEdit, setUserToEdit] = useState<User | null>(null);

    // Use the confirmation hook instead of custom modal state
    const confirmation = useConfirmation();

    // Get roles for the organization
    const { roles } = useRoles(organizationId || undefined);

    const {
      selectedOrganization,
      isSuperAdmin,
      isOrganizationAdmin,
      userOrganizationId,
    } = useAuth();

    const {
      users,
      loading,
      total,
      page,
      limit,
      searchName,
      statusFilter,
      fetchUsers,
      createUser,
      updateUser,
      deleteUser,
      search,
      filterByStatus,
      changePage,
      clearMessages,
      clearUpdateSuccess,
    } = useUsers();

    // Memoized columns definition
    const columns: Column<{ id: string } & User>[] = useMemo(() => {
      const baseColumns: Column<User>[] = [
        {
          key: 'name',
          label: 'User',
          render: (_: any, user: User) => (
            <div className='flex items-center space-x-3'>
              <div className='flex-shrink-0'>
                <div className='w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center'>
                  <UserIcon className='w-5 h-5 text-blue-600' />
                </div>
              </div>
              <div>
                <div className='font-medium text-gray-900'>
                  {user.name || `${user.firstName} ${user.lastName}`}
                </div>
                <div className='text-sm text-gray-500'>{user.email}</div>
              </div>
            </div>
          ),
        },
        {
          key: 'userRole',
          label: 'Role',
          render: (userRole: string) => (
            <span className='inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800'>
              {userRole?.replace('_', ' ') || 'N/A'}
            </span>
          ),
        },
        {
          key: 'status',
          label: 'Status',
          render: (status: 'active' | 'inactive') => (
            <StatusBadge status={status} size='sm' />
          ),
        },
      ];

      // Add organization column if no organization is selected (super admin view)
      if (!organizationId) {
        baseColumns.splice(1, 0, {
          key: 'organization',
          label: 'Organization',
          render: (_: any, user: User) => (
            <span className='text-sm text-gray-900'>
              {user.organizationName || 'N/A'}
            </span>
          ),
        } as Column<User>);
      }

      return baseColumns;
    }, [organizationId]);

    // Event handlers
    const handleOpenForm = useCallback(
      (user?: User) => {
        setUserToEdit(user || null);
        setIsFormOpen(true);
        clearMessages();
      },
      [clearMessages]
    );

    const handleCloseForm = useCallback(() => {
      setIsFormOpen(false);
      setUserToEdit(null);
      clearUpdateSuccess();
    }, [clearUpdateSuccess]);

    const handleDelete = useCallback(
      (user: User) => {
        const userName = user.name || `${user.firstName} ${user.lastName}`;
        confirmation.confirmDelete(
          userName,
          async () => {
            try {
              await deleteUser(user?.id || '');
              // Don't call fetchUsers here as the Redux slice will update the state
            } catch {
              // Error is already handled by the Redux slice
            }
          },
          `Are you sure you want to delete "${userName}"? This action cannot be undone and will permanently remove all associated data.`
        );
      },
      [confirmation, deleteUser]
    );

    const handleSubmit = useCallback(
      async (data: UserFormSchema) => {
        try {
          let currentOrgId: string | null = null;

          if (isSuperAdmin) {
            currentOrgId = selectedOrganization?.id || null;
          } else if (isOrganizationAdmin) {
            currentOrgId = userOrganizationId || null;
          }

          if (!currentOrgId) {
            return;
          }

          if (userToEdit) {
            const updateData: UpdateUserData = {
              id: userToEdit?.id || '',
              name: data.name,
              email: data.email,
              userRole: data.userRole,
              userType: data.userRole,
              roleId: data.roleId,
              status: data.status === 'active',
              organizationId: currentOrgId,
              consultationFee: data.consultationFee || null,
            };
            await updateUser(updateData);
          } else {
            // Find the selected role to get its ID
            const selectedRole = roles?.find(
              (role) => role.name === data.userRole
            );
            if (!selectedRole) {
              return;
            }

            const createData: CreateUserData = {
              name: data.name,
              email: data.email,
              userRole: data.userRole,
              userType: data.userRole,
              organizationId: currentOrgId,
              roleId: selectedRole.id,
              consultationFee: data.consultationFee || null,
            };
            const result = await createUser(createData);

            // Refresh the users list after successful creation
            if (result) {
              fetchUsers({
                organizationId: currentOrgId,
              });
            }
          }
        } catch {
          // Error is already handled by the Redux slice
        }
      },
      [
        userToEdit,
        updateUser,
        createUser,
        roles,
        fetchUsers,
        isOrganizationAdmin,
        isSuperAdmin,
        selectedOrganization?.id,
        userOrganizationId,
      ]
    );

    const handleSearchChange = useCallback(
      (searchTerm: string) => {
        search(searchTerm);
      },
      [search]
    );

    const handleStatusFilterChange = useCallback(
      (status: string) => {
        filterByStatus(status);
      },
      [filterByStatus]
    );

    const handlePageChange = useCallback(
      (newPage: number) => {
        changePage(newPage);
      },
      [changePage]
    );

    const handleFormSuccess = useCallback(() => {
      handleCloseForm();
      fetchUsers();
    }, [handleCloseForm, fetchUsers]);

    // Show error message if non-super admin and no organizationId
    const showOrgIdError = !isSuperAdmin && !organizationId;
    return (
      <div className='space-y-6'>
        {showOrgIdError && (
          <div className='p-4 text-red-600'>
            Error: Organization ID is required. Please contact support.
          </div>
        )}
        {!showOrgIdError && (
          <>
            <div className='flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4'>
              <div>
                <h1 className='text-2xl font-bold text-gray-900'>Users</h1>
                <p className='mt-1 text-sm text-gray-500'>
                  Manage users for {organizationName}
                </p>
              </div>
              <div className='flex items-center gap-3'>
                <SearchBar
                  value={searchName}
                  onChange={handleSearchChange}
                  placeholder='Search by name or email'
                  size='md'
                />
                {/* Only show Add User button if we have organization context and not a clinic organization or subscription organization */}
                {organizationId &&
                  !isClinicOrganization &&
                  selectedOrganization?.name !==
                    'Subscription - Organization' && (
                    <AppButton
                      onClick={() => handleOpenForm()}
                      startIcon={<Plus className='w-4 h-4' />}
                    >
                      Add User
                    </AppButton>
                  )}
              </div>
            </div>

            <SafeDataTable
              columns={columns}
              data={users}
              getId={(user) => user.id || ''}
              loading={loading}
              searchFilters={
                <div className='flex items-center space-x-3'>
                  <SelectInput
                    value={statusFilter}
                    onChange={(e) => handleStatusFilterChange(e.target.value)}
                    options={statusFilterOptions}
                    size='small'
                    sx={{ minWidth: 120 }}
                  />
                </div>
              }
              pagination={{
                total,
                page,
                limit,
                onPageChange: handlePageChange,
              }}
              {...(organizationId
                ? {
                    actions: (user: User) => {
                      const actions = [];

                      actions.push({
                        label: 'Edit',
                        icon: <Edit className='w-4 h-4' />,
                        onClick: user.isOrganizationMainAdmin
                          ? () => {}
                          : (item: User) => handleOpenForm(item),
                        disabled: user.isOrganizationMainAdmin ?? false,
                      });

                      actions.push({
                        label: 'Delete',
                        icon: <Trash2 className='w-4 h-4' />,
                        onClick: user.isOrganizationMainAdmin
                          ? () => {}
                          : (item: User) => handleDelete(item),
                        color: 'danger' as const,
                        disabled:
                          (user.isOrganizationMainAdmin ?? false) ||
                          (isClinicOrganization ?? false),
                      });

                      return actions;
                    },
                  }
                : {})}
            />

            {/* User Form Modal */}
            <Modal
              isOpen={isFormOpen}
              onClose={handleCloseForm}
              title={userToEdit ? 'Edit User' : 'Create User'}
              size='md'
            >
              <UserForm
                user={userToEdit}
                organizationId={organizationId}
                onSubmit={handleSubmit}
                onSuccess={handleFormSuccess}
                onCancel={handleCloseForm}
              />
            </Modal>

            {/* Confirmation Modal */}
            <ConfirmationModal
              isOpen={confirmation.isOpen}
              onClose={confirmation.hideConfirmation}
              onConfirm={confirmation.handleConfirm}
              title={confirmation.title}
              message={confirmation.message}
              confirmText={confirmation.confirmText || 'Confirm'}
              cancelText={confirmation.cancelText || 'Cancel'}
              type={confirmation.type || 'danger'}
              loading={confirmation.loading}
              {...(confirmation.itemName && {
                itemName: confirmation.itemName,
              })}
            />
          </>
        )}
      </div>
    );
  }
);

UserList.displayName = 'UserList';

export default UserList;
