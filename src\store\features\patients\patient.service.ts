import { PATIENTS_ENDPOINT } from '../../../constants/api-endpoints';
import api from '../../../services/api';

const apiUrl = PATIENTS_ENDPOINT;

export type PatientParams = {
  organizationId: string;
  searchText?: string;
  gender?: 'Male' | 'Female' | 'Other';
  fromAge?: number;
  toAge?: number;
  fromDate?: string; // Format: YYYY-MM-DD
  toDate?: string; // Format: YYYY-MM-DD
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
  pageSize?: number;
  page?: number;
};

export type PatientListResponse = {
  items: any[]; // Will be typed as Patient[] when used
  pagination: {
    currentPage: number;
    pageSize: number;
    totalItems: number;
    totalPages: number;
    hasNextPage: boolean;
    hasPreviousPage: boolean;
  };
};

export type FetchPatientByIdParams = {
  organizationId: string;
  patientId: string;
};

const patientService = {
  async fetchPatients(params: PatientParams): Promise<PatientListResponse> {
    const queryParams = new URLSearchParams();

    // Add required organizationId
    queryParams.append('organizationId', params.organizationId);

    // Add optional parameters
    if (params.searchText)
      queryParams.append('searchText', params.searchText.trim());
    if (params.gender) queryParams.append('gender', params.gender);
    if (params.fromAge !== undefined)
      queryParams.append('fromAge', params.fromAge.toString());
    if (params.toAge !== undefined)
      queryParams.append('toAge', params.toAge.toString());
    if (params.fromDate) queryParams.append('fromDate', params.fromDate);
    if (params.toDate) queryParams.append('toDate', params.toDate);
    if (params.sortBy) queryParams.append('sortBy', params.sortBy);
    if (params.sortOrder) queryParams.append('sortOrder', params.sortOrder);
    if (params.pageSize)
      queryParams.append('pageSize', params.pageSize.toString());
    if (params.page) queryParams.append('page', params.page.toString());

    const response = await api.get(
      `${apiUrl}/patients?${queryParams.toString()}`
    );
    return response.data;
  },

  async fetchPatientById(params: FetchPatientByIdParams) {
    const response = await api.get(`${apiUrl}/patients/${params.patientId}`, {
      params: {
        organizationId: params.organizationId,
      },
    });
    return response.data;
  },
};

export default patientService;
