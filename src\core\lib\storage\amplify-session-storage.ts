import { KeyValueStorageInterface } from 'aws-amplify/utils';

export const amplifySessionStorage: KeyValueStorageInterface = {
  setItem: async (key, value) => {
    if (typeof window !== 'undefined') sessionStorage.setItem(key, value);
  },
  getItem: async (key) => {
    return typeof window !== 'undefined' ? sessionStorage.getItem(key) : null;
  },
  removeItem: async (key) => {
    if (typeof window !== 'undefined') sessionStorage.removeItem(key);
  },
  clear: async () => {
    if (typeof window !== 'undefined') {
      // Clear only Amplify/Cognito keys
      Object.keys(sessionStorage)
        .filter((key) => key.startsWith('Cognito') || key.startsWith('amplify'))
        .forEach((key) => sessionStorage.removeItem(key));
    }
  },
};
